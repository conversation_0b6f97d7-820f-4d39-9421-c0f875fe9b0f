#!/usr/bin/env python3
"""
SAP HANA SQL Analyzer - Application Runner
==========================================

This script provides easy deployment and running options for the Streamlit application.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                      check=True)
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_environment():
    """Setup environment variables"""
    print("⚙️ Setting up environment...")
    
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if not env_file.exists() and env_example.exists():
        print("📝 Creating .env file from template...")
        with open(env_example, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
        print("✅ .env file created. Please update with your API keys.")
    
    return True

def run_streamlit(port=8501, host='localhost'):
    """Run the Streamlit application"""
    print(f"🚀 Starting Streamlit application on {host}:{port}")
    
    try:
        cmd = [
            sys.executable, '-m', 'streamlit', 'run', 'app.py',
            '--server.port', str(port),
            '--server.address', host,
            '--server.headless', 'true',
            '--browser.gatherUsageStats', 'false'
        ]
        
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start Streamlit: {e}")
        return False
    except KeyboardInterrupt:
        print("\n👋 Application stopped by user")
        return True

def check_prerequisites():
    """Check if all prerequisites are met"""
    print("🔍 Checking prerequisites...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Check if pip is available
    try:
        subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                      capture_output=True, check=True)
        print("✅ pip is available")
    except subprocess.CalledProcessError:
        print("❌ pip is not available")
        return False
    
    return True

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='SAP HANA SQL Analyzer Runner')
    parser.add_argument('--port', type=int, default=8501, help='Port to run the application on')
    parser.add_argument('--host', default='localhost', help='Host to run the application on')
    parser.add_argument('--install-only', action='store_true', help='Only install dependencies')
    parser.add_argument('--setup-only', action='store_true', help='Only setup environment')
    
    args = parser.parse_args()
    
    print("🔍 SAP HANA SQL Analyzer")
    print("=" * 40)
    
    # Check prerequisites
    if not check_prerequisites():
        sys.exit(1)
    
    # Setup environment
    if not setup_environment():
        sys.exit(1)
    
    if args.setup_only:
        print("✅ Environment setup complete!")
        return
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    if args.install_only:
        print("✅ Dependencies installation complete!")
        return
    
    # Run application
    print("\n🚀 Starting application...")
    print(f"📱 Open your browser to: http://{args.host}:{args.port}")
    print("⏹️  Press Ctrl+C to stop the application")
    
    run_streamlit(args.port, args.host)

if __name__ == "__main__":
    main()
