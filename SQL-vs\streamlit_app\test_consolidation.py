#!/usr/bin/env python3
"""
Test Table Consolidation Feature
================================

Test the new table consolidation functionality that shows one row per table
with consolidated information from all procedures.
"""

def test_table_consolidation():
    """Test the table consolidation feature"""
    print("🔍 Testing Table Consolidation Feature...")
    
    try:
        from sql_parser import AdvancedSQLParser
        
        parser = AdvancedSQLParser()
        
        # Sample procedures with overlapping tables
        sample_procedures = [
            {
                'id': 'PROC_1',
                'sql': '''
                PROCEDURE "PROC_1" AS
                BEGIN
                SELECT VBAK.VBELN, VBAP.POSNR
                FROM VBAK 
                INNER JOIN VBAP ON VBAK.VBELN = VBAP.VBELN
                WHERE VBAK.VBTYP = 'C'
                AND VBAP.ERDAT > '20240101';
                END;
                '''
            },
            {
                'id': 'PROC_2', 
                'sql': '''
                PROCEDURE "PROC_2" AS
                BEGIN
                SELECT VBAK.VBELN, VBRK.VBELN
                FROM VBAK
                INNER JOIN VBRK ON VBAK.VBELN = VBRK.AUBEL
                WHERE VBAK.VBTYP IN ('C', 'D')
                AND VBRK.FKDAT > '20240101';
                END;
                '''
            }
        ]
        
        # Parse procedures
        parsed_procedures = []
        for proc in sample_procedures:
            analysis = parser.parse_procedure(proc['sql'], proc['id'])
            parsed_procedures.append(analysis)
        
        print(f"✅ Parsed {len(parsed_procedures)} procedures")
        
        # Test cross-procedure analysis
        cross_analysis = parser.analyze_cross_procedure_patterns(parsed_procedures)
        
        print("✅ Cross-procedure analysis completed")
        
        # Check table consolidation
        table_consolidation = cross_analysis.get('table_consolidation', {})
        
        print(f"\n📊 Table Consolidation Results:")
        print(f"   Unique tables found: {len(table_consolidation)}")
        
        for table_name, table_data in table_consolidation.items():
            print(f"\n🔹 Table: {table_name}")
            print(f"   Used in procedures: {table_data['used_in_procedures']}")
            print(f"   Procedure count: {table_data['procedure_count']}")
            print(f"   Migration priority: {table_data['migration_priority']}")
            print(f"   Columns: {table_data['all_columns']}")
            
            # Show filter consolidation
            filter_info = table_data['consolidated_filters']
            if filter_info['has_filters']:
                print(f"   Filter patterns: {len(filter_info['patterns'])}")
                for pattern in filter_info['patterns']:
                    print(f"     - {pattern['column']}: {pattern['migration_note']}")
            
            # Show join consolidation
            join_info = table_data['consolidated_joins']
            if join_info['has_joins']:
                print(f"   Join relationships: {len(join_info['relationships'])}")
                for rel in join_info['relationships']:
                    print(f"     - → {rel['related_table']}: {rel['migration_note']}")
        
        print("\n🎉 Table consolidation test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Table consolidation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run consolidation test"""
    print("🧪 SAP HANA Table Consolidation Test")
    print("=" * 40)
    
    if test_table_consolidation():
        print("\n✅ All tests passed!")
        print("🚀 Ready to run the Streamlit app with table consolidation")
    else:
        print("\n❌ Tests failed!")

if __name__ == "__main__":
    main()
