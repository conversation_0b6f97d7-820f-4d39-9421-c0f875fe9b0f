#!/usr/bin/env python3
"""
Debug Column Extraction
=======================

Debug why columns are not being extracted from aliased tables.
"""

import re

sql = '''
SELECT 
    h.bukrs as company_code,
    h.belnr as document_number,
    h.bldat as document_date,
    i.lifnr as vendor_number,
    i.dmbtr as amount_local_currency,
    i.koart as account_type
FROM bkpf h
INNER JOIN bseg i ON h.belnr = i.belnr 
WHERE h.bukrs = 'US01'
  AND i.koart = 'K'
  AND i.lifnr IS NOT NULL;
'''

print("🔍 Debug Column Extraction")
print("=" * 30)

# Manual regex test for h.bukrs pattern
print("\n1. Manual regex test for 'h.bukrs':")
pattern = r'h\.(\w+)'
matches = re.findall(pattern, sql, re.IGNORECASE)
print(f"   Matches: {matches}")

# Manual regex test for i.lifnr pattern  
print("\n2. Manual regex test for 'i.lifnr':")
pattern = r'i\.(\w+)'
matches = re.findall(pattern, sql, re.IGNORECASE)
print(f"   Matches: {matches}")

# Test all table.column patterns
print("\n3. All table.column patterns:")
pattern = r'(\w+)\.(\w+)'
matches = re.findall(pattern, sql, re.IGNORECASE)
print(f"   All matches: {matches}")

# Group by table/alias
from collections import defaultdict
columns_by_alias = defaultdict(list)
for table_alias, column in matches:
    columns_by_alias[table_alias.upper()].append(column.upper())

print(f"\n4. Grouped by alias:")
for alias, columns in columns_by_alias.items():
    print(f"   {alias}: {columns}")

# Test the actual parser method
print("\n5. Testing parser method:")
from sql_parser import AdvancedSQLParser

parser = AdvancedSQLParser()

# Test alias mapping
aliases = parser._build_alias_mapping(sql)
print(f"   Alias mapping: {aliases}")

# Test column extraction with debug
def debug_extract_columns_for_table(sql_text: str, table_name: str):
    """Debug version of column extraction"""
    columns = set()
    
    # Use the same alias mapping as filters
    alias_to_table = parser._build_alias_mapping(sql_text)
    print(f"   Alias mapping for {table_name}: {alias_to_table}")
    
    # Find aliases for this table
    table_aliases = set()
    for alias, table in alias_to_table.items():
        if table == table_name:
            table_aliases.add(alias)
    
    print(f"   Aliases for {table_name}: {table_aliases}")
    
    # Pattern for direct table.column references
    column_pattern = rf'{table_name}\.(\w+)'
    matches = re.finditer(column_pattern, sql_text, re.IGNORECASE)
    
    direct_matches = []
    for match in matches:
        column = match.group(1).upper()
        columns.add(column)
        direct_matches.append(column)
    
    print(f"   Direct {table_name}.column matches: {direct_matches}")
    
    # Pattern for aliased table.column references
    for alias in table_aliases:
        # Try both uppercase and lowercase alias patterns
        for alias_variant in [alias, alias.lower()]:
            alias_pattern = rf'{re.escape(alias_variant)}\.(\w+)'
            print(f"   Trying pattern: {alias_pattern}")
            alias_matches = re.finditer(alias_pattern, sql_text, re.IGNORECASE)
            
            alias_found = []
            for match in alias_matches:
                column = match.group(1).upper()
                columns.add(column)
                alias_found.append(column)
            
            print(f"   Alias {alias_variant} matches: {alias_found}")
    
    return columns

print("\n6. Debug BSEG column extraction:")
bseg_columns = debug_extract_columns_for_table(sql, 'BSEG')
print(f"   Final BSEG columns: {sorted(list(bseg_columns))}")

print("\n7. Debug BKPF column extraction:")
bkpf_columns = debug_extract_columns_for_table(sql, 'BKPF')
print(f"   Final BKPF columns: {sorted(list(bkpf_columns))}")
