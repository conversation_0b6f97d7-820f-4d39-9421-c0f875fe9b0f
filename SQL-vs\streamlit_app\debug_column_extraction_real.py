#!/usr/bin/env python3
"""
Debug Column Extraction Real
============================

Debug why columns are not being extracted from the real BSEG SQL.
"""

import re

def debug_column_extraction():
    """Debug column extraction from real BSEG SQL"""
    
    real_bseg_sql = '''
CREATE PROCEDURE PR_BSEG_ACTIVITY_LOAD
(
    IN IP_BUDAT_FROM DATE,
    IN IP_BUDAT_TO DATE,
    IN IP_BUKRS VARCHAR(4)
)
LANGUAGE SQL
AS
BEGIN
    /*
    Purpose: Extract BSEG activities for OTC analysis
    Tables: BSEG (FI Document Line Items)
    */
    
    TRUNCATE TABLE CT_BSEG_DELTA_ACTIVITIES;
    
    INSERT INTO CT_BSEG_DELTA_ACTIVITIES
    (
        MANDT,
        BUKRS,
        BELNR,
        GJAHR,
        BUZEI,
        BUDAT,
        BLART,
        TCODE,
        SHKZG,
        WRBTR,
        PARTITION_ID,
        CREATED_AT
    )
    SELECT 
        MANDT,
        BUKRS,
        BELNR,
        GJAHR,
        BUZEI,
        BUDAT,
        BLART,
        TCODE,
        SHKZG,
        WRBTR,
        1 as PARTITION_ID,
        CURRENT_TIMESTAMP as CREATED_AT
    FROM BSEG
    WHERE BUDAT BETWEEN :IP_BUDAT_FROM AND :IP_BUDAT_TO
    AND BUKRS = :IP_BUKRS
    AND BLART IN ('SA', 'KR', 'DR');
END;
    '''
    
    print("🔍 Debug Column Extraction from Real BSEG SQL")
    print("=" * 50)
    
    # Test 1: Basic SELECT pattern matching
    print("\n1. Testing SELECT pattern matching:")
    select_pattern = r'SELECT\s+(.*?)\s+FROM\s+(\w+)'
    matches = re.finditer(select_pattern, real_bseg_sql, re.IGNORECASE | re.DOTALL)
    
    for i, match in enumerate(matches):
        print(f"   Match {i+1}:")
        columns_text = match.group(1)
        table_name = match.group(2)
        print(f"     Table: {table_name}")
        print(f"     Columns text: {columns_text[:100]}...")
        
        # Extract individual columns
        column_list = [col.strip() for col in columns_text.split(',')]
        print(f"     Raw columns: {column_list[:5]}...")  # Show first 5
        
        # Clean columns
        clean_columns = []
        for column in column_list:
            # Remove aliases (AS keyword)
            column = re.sub(r'\s+as\s+\w+', '', column, flags=re.IGNORECASE)
            column = column.strip()
            
            # Skip complex expressions
            if (column and 
                not any(char in column for char in ['(', ')', '+', '-', '*', '/', '\'', '"']) and
                column.upper() not in ['CURRENT_TIMESTAMP', 'CURRENT_DATE'] and
                not column.isdigit()):
                
                column_upper = column.upper()
                if len(column_upper) >= 2 and column_upper.replace('_', '').isalnum():
                    clean_columns.append(column_upper)
        
        print(f"     Clean columns: {clean_columns}")
    
    # Test 2: Manual column extraction
    print("\n2. Manual column extraction from SELECT:")
    
    # Find the SELECT statement
    select_start = real_bseg_sql.upper().find('SELECT')
    from_start = real_bseg_sql.upper().find('FROM BSEG')
    
    if select_start != -1 and from_start != -1:
        select_columns = real_bseg_sql[select_start + 6:from_start].strip()
        print(f"   SELECT columns section: {select_columns}")
        
        # Split by comma and clean
        columns = [col.strip() for col in select_columns.split(',')]
        print(f"   Split columns: {columns}")
        
        # Clean each column
        final_columns = []
        for col in columns:
            # Remove AS aliases
            col = re.sub(r'\s+as\s+\w+', '', col, flags=re.IGNORECASE).strip()
            
            # Skip expressions with functions or numbers
            if (not any(char in col for char in ['(', ')', '+', '-', '*', '/', '\'', '"']) and
                not col.isdigit() and
                col.upper() not in ['CURRENT_TIMESTAMP', 'CURRENT_DATE']):
                
                final_columns.append(col.upper())
        
        print(f"   Final BSEG columns: {final_columns}")
    
    # Test 3: Test the actual enhanced listener method
    print("\n3. Testing enhanced listener method:")
    
    # Simulate the enhanced listener
    class TestListener:
        def __init__(self):
            self.tables = set()
            self.columns = {}
            self.current_sql_text = real_bseg_sql
        
        def _extract_select_columns(self, sql: str):
            """Extract columns from SELECT statements, especially INSERT...SELECT"""
            from collections import defaultdict
            self.columns = defaultdict(set)
            
            # Pattern for SELECT columns in INSERT...SELECT statements
            select_pattern = r'SELECT\s+(.*?)\s+FROM\s+(\w+)'
            matches = re.finditer(select_pattern, sql, re.IGNORECASE | re.DOTALL)
            
            for match in matches:
                columns_text = match.group(1)
                table_name = match.group(2).upper()
                
                print(f"     Found SELECT from {table_name}")
                print(f"     Columns text: {columns_text[:100]}...")
                
                # Add the table
                self.tables.add(table_name)
                
                # Extract individual columns
                column_list = [col.strip() for col in columns_text.split(',')]
                
                for column in column_list:
                    # Remove aliases (AS keyword)
                    column = re.sub(r'\s+as\s+\w+', '', column, flags=re.IGNORECASE)
                    column = column.strip()
                    
                    # Skip complex expressions, keep simple column names
                    if (column and 
                        not any(char in column for char in ['(', ')', '+', '-', '*', '/', '\'', '"']) and
                        column.upper() not in ['CURRENT_TIMESTAMP', 'CURRENT_DATE'] and
                        not column.isdigit()):
                        
                        column_upper = column.upper()
                        if len(column_upper) >= 2 and column_upper.replace('_', '').isalnum():
                            self.columns[table_name].add(column_upper)
                            print(f"       Added column: {column_upper}")
    
    listener = TestListener()
    listener._extract_select_columns(real_bseg_sql)
    
    print(f"   Tables found: {listener.tables}")
    print(f"   Columns found: {dict(listener.columns)}")
    
    # Expected columns from the SQL
    expected_columns = ['MANDT', 'BUKRS', 'BELNR', 'GJAHR', 'BUZEI', 'BUDAT', 'BLART', 'TCODE', 'SHKZG', 'WRBTR']
    if 'BSEG' in listener.columns:
        found_columns = list(listener.columns['BSEG'])
        missing = [col for col in expected_columns if col not in found_columns]
        print(f"   Expected: {expected_columns}")
        print(f"   Found: {found_columns}")
        print(f"   Missing: {missing}")
    
    print("\n✅ Debug completed!")

if __name__ == "__main__":
    debug_column_extraction()
