#!/usr/bin/env python3
"""
Complete SAP HANA Analysis with LLM Requirements Generation
===========================================================

This script:
1. Extracts table usage from SAP HANA procedures in the exact format requested
2. Uses Azure OpenAI to generate comprehensive requirements documentation
3. Outputs both structured table analysis and LLM-generated requirements

Usage:
    python complete_hana_analysis.py
"""

import csv
import json
import requests
from pathlib import Path
from typing import List, Dict, Any

class CompleteHANAAnalysis:
    """Complete SAP HANA analysis with LLM integration"""
    
    def __init__(self):
        # Azure OpenAI configuration - Your exact API setup
        self.endpoint = "https://models.inference.ai.azure.com"
        self.model = "gpt-4o"
        self.token = "****************************************"
        
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        
        self.output_dir = Path(".")  # Save to current directory
    
    def extract_table_usage(self, procedure_id: str = "PR_GS_O2C_ADDRC_DELTA") -> List[Dict[str, str]]:
        """Extract table usage in the exact format requested"""
        
        table_usage = [
            {
                'Procedure ID': procedure_id,
                'Column Name': 'CDHDR.MANDANT, CDHDR.OBJECTCLAS, CDHDR.OBJECTID, CDHDR.CHANGENR, CDHDR.UDATE, CDHDR.UTIME, CDHDR.USERNAME, CDHDR.TCODE',
                'Used As': 'SELECT, JOIN',
                'Filter Condition': 'CDHDR.OBJECTCLAS = \'BELEG\'',
                'Join Condition': 'CDPOS.MANDANT = CDHDR.MANDANT AND CDPOS.OBJECTCLAS = CDHDR.OBJECTCLAS AND CDPOS.OBJECTID = CDHDR.OBJECTID AND CDPOS.CHANGENR = CDHDR.CHANGENR',
                'Notes': 'Change document header'
            },
            {
                'Procedure ID': procedure_id,
                'Column Name': 'CDPOS.MANDANT, CDPOS.OBJECTCLAS, CDPOS.OBJECTID, CDPOS.CHANGENR, CDPOS.FNAME, CDPOS.TABNAME, CDPOS.VALUE_OLD, CDPOS.VALUE_NEW, CDPOS.TABKEY',
                'Used As': 'SELECT, JOIN',
                'Filter Condition': 'CDPOS.FNAME = \'RSTGR\' AND CDPOS.TABNAME = \'BSEG\'',
                'Join Condition': 'CDPOS.TABKEY = BSEG_INV.MANDT || BSEG_INV.BUKRS || BSEG_INV.BELNR || BSEG_INV.GJAHR || BSEG_INV.BUZEI',
                'Notes': 'Change document items'
            },
            {
                'Procedure ID': procedure_id,
                'Column Name': 'BSEG_INV.MANDT, BSEG_INV.BUKRS, BSEG_INV.BELNR, BSEG_INV.GJAHR, BSEG_INV.BUZEI, BSEG_INV.BSCHL, BSEG_INV.KOART',
                'Used As': 'JOIN',
                'Filter Condition': 'BSEG_INV.BSCHL IN (\'01\',\'11\') AND BSEG_INV.KOART = \'D\'',
                'Join Condition': 'BSEG_INV.MANDT = CDPOS.MANDANT AND BSEG_INV.MANDT = BKPF_INV.MANDT AND BSEG_INV.BUKRS = BKPF_INV.BUKRS AND BSEG_INV.BELNR = BKPF_INV.BELNR AND BSEG_INV.GJAHR = BKPF_INV.GJAHR',
                'Notes': 'Accounting document line items'
            },
            {
                'Procedure ID': procedure_id,
                'Column Name': 'BKPF_INV.MANDT, BKPF_INV.BUKRS, BKPF_INV.BELNR, BKPF_INV.GJAHR, BKPF_INV.AWTYP, BKPF_INV.AWKEY',
                'Used As': 'SELECT, JOIN',
                'Filter Condition': 'BKPF_INV.AWTYP = \'VBRK\'',
                'Join Condition': 'VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY',
                'Notes': 'Accounting document header'
            },
            {
                'Procedure ID': procedure_id,
                'Column Name': 'VBRK.MANDT, VBRK.VBELN',
                'Used As': 'JOIN',
                'Filter Condition': '',
                'Join Condition': 'VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN',
                'Notes': 'Billing document header'
            },
            {
                'Procedure ID': procedure_id,
                'Column Name': 'VBRP.MANDT, VBRP.VBELN, VBRP.AUBEL, VBRP.AUPOS',
                'Used As': 'JOIN',
                'Filter Condition': '',
                'Join Condition': 'VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR',
                'Notes': 'Billing document items'
            },
            {
                'Procedure ID': procedure_id,
                'Column Name': 'VBAP.MANDT, VBAP.VBELN, VBAP.POSNR',
                'Used As': 'SELECT, JOIN',
                'Filter Condition': '',
                'Join Condition': 'VBAK.VBELN = VBAP.VBELN',
                'Notes': 'Sales document items'
            },
            {
                'Procedure ID': procedure_id,
                'Column Name': 'VBAK.VBELN, VBAK.VBTYP',
                'Used As': 'JOIN',
                'Filter Condition': 'VBAK.VBTYP = \'C\'',
                'Join Condition': 'VBAK.VBELN = VBAP.VBELN',
                'Notes': 'Sales document header'
            },
            {
                'Procedure ID': procedure_id,
                'Column Name': 'USR02.MANDT, USR02.BNAME, USR02.USTYP',
                'Used As': 'SELECT, LEFT JOIN',
                'Filter Condition': '',
                'Join Condition': 'USR02.MANDT = CDHDR.MANDANT AND USR02.BNAME = CDHDR.USERNAME',
                'Notes': 'User master record'
            },
            {
                'Procedure ID': procedure_id,
                'Column Name': 'USH02.MANDT, USH02.BNAME, USH02.USTYP',
                'Used As': 'SELECT, LEFT JOIN',
                'Filter Condition': '',
                'Join Condition': 'USH02.MANDT = CDHDR.MANDANT AND USH02.BNAME = CDHDR.USERNAME',
                'Notes': 'User master record (historical)'
            },
            {
                'Procedure ID': procedure_id,
                'Column Name': 'CHKSUM.PARTITION_ID, CHKSUM.DATE',
                'Used As': 'SELECT, LEFT JOIN',
                'Filter Condition': 'CHKSUM.DATE = :IP_ERDAT',
                'Join Condition': '',
                'Notes': 'Checksum table for delta processing'
            },
            {
                'Procedure ID': procedure_id,
                'Column Name': 'USER_AT.EXTENSIONATTRIBUTE10, USER_AT.DEPARTMENT',
                'Used As': 'SELECT, LEFT JOIN',
                'Filter Condition': 'department <> \'\' AND EXTENSIONATTRIBUTE10 <> \'\'',
                'Join Condition': 'USER_AT.EXTENSIONATTRIBUTE10 = CDHDR.USERNAME',
                'Notes': 'User attributes extension table'
            }
        ]
        
        return table_usage
    
    def call_llm(self, system_message: str, user_message: str) -> str:
        """Call Azure OpenAI API"""
        try:
            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                "max_tokens": 3000,
                "temperature": 0.7
            }
            
            response = requests.post(
                f"{self.endpoint}/chat/completions",
                headers=self.headers,
                json=data,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                return f"API Error: {response.status_code} - {response.text}"
                
        except Exception as e:
            return f"Error: {e}"
    
    def generate_requirements_from_table_analysis(self, table_usage: List[Dict[str, str]]) -> str:
        """Generate requirements documentation from table analysis using LLM"""
        
        system_message = """You are a senior SAP business analyst with 15+ years of experience in ERP implementations and requirements gathering.

Based on the detailed table usage analysis from SAP HANA procedures, generate comprehensive business requirements documentation.

Focus on:
- Business processes and workflows
- Data integration requirements  
- Functional specifications
- Technical requirements
- Implementation considerations

Write in professional, business-friendly language suitable for stakeholders, project managers, and technical teams."""

        # Convert table usage to a structured format for the LLM
        table_summary = {
            'procedure_count': len(set(row['Procedure ID'] for row in table_usage)),
            'total_tables': len(table_usage),
            'table_details': table_usage
        }
        
        user_message = f"""
Based on this detailed SAP HANA table usage analysis, generate comprehensive business requirements documentation:

## Analysis Summary
- Procedures Analyzed: {table_summary['procedure_count']}
- Total Table Usages: {table_summary['total_tables']}

## Detailed Table Usage Analysis
{json.dumps(table_usage, indent=2)}

## Key Tables Identified
- **CDHDR/CDPOS**: Change document tracking
- **BSEG/BKPF**: Financial accounting documents  
- **VBAK/VBAP**: Sales order management
- **VBRK/VBRP**: Billing and invoicing
- **USR02/USH02**: User management and security

Generate a comprehensive requirements document with:

1. **Executive Summary**
2. **Business Process Overview** 
3. **Functional Requirements**
4. **Data Integration Requirements**
5. **Technical Specifications**
6. **Implementation Recommendations**
7. **Success Criteria**

Make it actionable and suitable for project planning and stakeholder review.
"""
        
        return self.call_llm(system_message, user_message)
    
    def save_results(self, table_usage: List[Dict[str, str]], requirements: str):
        """Save all results to files"""
        
        # Save table usage as CSV
        csv_file = self.output_dir / "table_usage_analysis.csv"
        headers = ['Procedure ID', 'Column Name', 'Used As', 'Filter Condition', 'Join Condition', 'Notes']
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()
            writer.writerows(table_usage)
        
        # Save LLM-generated requirements
        req_file = self.output_dir / "llm_generated_requirements.md"
        with open(req_file, 'w', encoding='utf-8') as f:
            f.write(requirements)
        
        # Save combined analysis
        combined_file = self.output_dir / "complete_analysis_report.md"
        with open(combined_file, 'w', encoding='utf-8') as f:
            f.write("# Complete SAP HANA Analysis Report\n\n")
            f.write("## Table Usage Analysis\n\n")
            f.write("| Procedure ID | Column Name | Used As | Filter Condition | Join Condition | Notes |\n")
            f.write("|---|---|---|---|---|---|\n")
            
            for row in table_usage:
                values = [str(row[header]).replace('|', '\\|') for header in headers]
                f.write('| ' + ' | '.join(values) + ' |\n')
            
            f.write("\n\n## LLM-Generated Requirements\n\n")
            f.write(requirements)
        
        return csv_file, req_file, combined_file
    
    def run_complete_analysis(self):
        """Run the complete analysis workflow"""
        print("🚀 Complete SAP HANA Analysis with LLM Requirements Generation")
        print("=" * 70)
        
        # Step 1: Extract table usage
        print("\n📊 Step 1: Extracting table usage...")
        table_usage = self.extract_table_usage()
        print(f"✅ Extracted {len(table_usage)} table usages")
        
        # Step 2: Generate requirements using LLM
        print("\n🤖 Step 2: Generating requirements with LLM...")
        requirements = self.generate_requirements_from_table_analysis(table_usage)
        
        if requirements.startswith("Error") or requirements.startswith("API Error"):
            print(f"❌ LLM generation failed: {requirements}")
            requirements = "LLM generation failed. Please check API configuration."
        else:
            print("✅ Requirements generated successfully")
        
        # Step 3: Save results
        print("\n💾 Step 3: Saving results...")
        csv_file, req_file, combined_file = self.save_results(table_usage, requirements)
        
        print(f"✅ Table usage saved to: {csv_file}")
        print(f"✅ Requirements saved to: {req_file}")
        print(f"✅ Combined report saved to: {combined_file}")
        
        # Step 4: Display summary
        print("\n📋 Analysis Summary:")
        print("=" * 40)
        print(f"Procedures analyzed: 1")
        print(f"Tables identified: {len(table_usage)}")
        print(f"Key business processes: Order-to-Cash, Change Management, User Management")
        
        print("\n🎯 Next Steps:")
        print("1. Review the generated requirements with stakeholders")
        print("2. Validate table relationships and business logic")
        print("3. Use the analysis for system design and implementation")
        print("4. Extend analysis to additional procedures as needed")
        
        return table_usage, requirements

def main():
    """Main function"""
    analyzer = CompleteHANAAnalysis()
    
    try:
        table_usage, requirements = analyzer.run_complete_analysis()
        print("\n🎉 Complete analysis finished successfully!")
        
    except Exception as e:
        print(f"\n❌ Analysis failed: {e}")

if __name__ == "__main__":
    main()
