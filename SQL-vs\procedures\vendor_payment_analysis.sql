
CREATE PROCEDURE vendor_payment_analysis(
    IN p_company_code NVARCHAR(4),
    IN p_fiscal_year NVARCHAR(4)
)
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
READS SQL DATA AS
BEGIN
    SELECT 
        h.bukrs as company_code,
        h.belnr as document_number,
        h.gjahr as fiscal_year,
        h.bldat as document_date,
        h.budat as posting_date,
        i.lifnr as vendor_number,
        v.name1 as vendor_name,
        i.dmbtr as amount_local_currency,
        i.wrbtr as amount_document_currency,
        i.waers as currency,
        i.zfbdt as baseline_date,
        i.zbd1t as cash_discount_days,
        i.zbd2t as payment_terms_days,
        CASE 
            WHEN DAYS_BETWEEN(i.zfbdt, CURRENT_DATE) <= i.zbd1t 
            THEN 'DISCOUNT_PERIOD'
            WHEN DAYS_BETWEEN(i.zfbdt, CURRENT_DATE) <= i.zbd2t 
            THEN 'PAYMENT_PERIOD'
            ELSE 'OVERDUE'
        END as payment_status
    FROM bkpf h
    INNER JOIN bseg i ON h.belnr = i.belnr 
                     AND h.gjahr = i.gjahr 
                     AND h.bukrs = i.bukrs
    INNER JOIN lfa1 v ON i.lifnr = v.lifnr
    WHERE h.bukrs = p_company_code
      AND h.gjahr = p_fiscal_year
      AND i.koart = 'K'
      AND i.lifnr IS NOT NULL
    ORDER BY h.budat DESC, i.lifnr;
END;
