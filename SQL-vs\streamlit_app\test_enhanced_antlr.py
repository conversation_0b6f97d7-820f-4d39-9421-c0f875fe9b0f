#!/usr/bin/env python3
"""
Test Enhanced ANTLR
===================

Test the enhanced ANTLR parsing with BSEG procedure.
"""

def test_enhanced_antlr():
    """Test enhanced ANTLR parsing"""
    print("🔍 Testing Enhanced ANTLR Parsing...")
    
    try:
        from sql_parser import AdvancedSQLParser
        
        parser = AdvancedSQLParser()
        
        # Real BSEG procedure SQL
        bseg_sql = '''
        CREATE PROCEDURE PR_BSEG_ACTIVITY_LOAD(
            IN p_company_code NVARCHAR(4),
            IN p_fiscal_year NVARCHAR(4)
        )
        LANGUAGE SQLSCRIPT
        SQL SECURITY INVOKER
        READS SQL DATA AS
        BEGIN
            SELECT 
                h.bukrs as company_code,
                h.belnr as document_number,
                h.gjahr as fiscal_year,
                h.bldat as document_date,
                h.budat as posting_date,
                i.lifnr as vendor_number,
                i.kunnr as customer_number,
                i.dmbtr as amount_local_currency,
                i.wrbtr as amount_document_currency,
                i.waers as currency,
                i.koart as account_type,
                i.shkzg as debit_credit_indicator,
                i.zfbdt as baseline_date,
                i.zbd1t as cash_discount_days,
                i.zbd2t as payment_terms_days
            FROM bkpf h
            INNER JOIN bseg i ON h.belnr = i.belnr 
                             AND h.gjahr = i.gjahr 
                             AND h.bukrs = i.bukrs
            LEFT JOIN lfa1 v ON i.lifnr = v.lifnr
            LEFT JOIN kna1 c ON i.kunnr = c.kunnr
            WHERE h.bukrs = p_company_code
              AND h.gjahr = p_fiscal_year
              AND i.koart IN ('K', 'D')
              AND (i.lifnr IS NOT NULL OR i.kunnr IS NOT NULL)
            ORDER BY h.budat DESC, i.lifnr, i.kunnr;
        END;
        '''
        
        print("📝 Parsing BSEG procedure with enhanced ANTLR...")
        result = parser.parse_procedure(bseg_sql, "PR_BSEG_ACTIVITY_LOAD")
        
        print(f"\n📊 Enhanced ANTLR Results:")
        print(f"   Tables found: {list(result.tables.keys())}")
        print(f"   Filters found: {len(result.filters)}")
        print(f"   Joins found: {len(result.joins)}")
        
        # Check each table's details
        for table_name, table_ref in result.tables.items():
            print(f"\n🔹 Table: {table_name}")
            print(f"   Columns: {sorted(list(table_ref.columns))}")
            print(f"   Usage contexts: {table_ref.usage_contexts}")
        
        # Check BSEG specifically
        if 'BSEG' in result.tables:
            bseg_table = result.tables['BSEG']
            bseg_columns = sorted(list(bseg_table.columns))
            print(f"\n✅ BSEG Table Found!")
            print(f"   BSEG columns: {bseg_columns}")
            print(f"   Expected columns: LIFNR, KUNNR, DMBTR, WRBTR, KOART, SHKZG, etc.")
            
            # Check if key columns are found
            key_columns = ['LIFNR', 'KUNNR', 'DMBTR', 'WRBTR', 'KOART', 'SHKZG', 'WAERS']
            found_key_columns = [col for col in key_columns if col in bseg_columns]
            missing_key_columns = [col for col in key_columns if col not in bseg_columns]
            
            print(f"   Found key columns: {found_key_columns}")
            if missing_key_columns:
                print(f"   Missing key columns: {missing_key_columns}")
            else:
                print(f"   ✅ All key columns found!")
        else:
            print(f"\n❌ BSEG Table NOT Found!")
        
        # Check filters
        print(f"\n🔍 Filter Details:")
        for i, filter_cond in enumerate(result.filters):
            print(f"   Filter {i+1}: {filter_cond.table}.{filter_cond.column} {filter_cond.operator} {filter_cond.values}")
        
        # Check joins
        print(f"\n🔗 Join Details:")
        for i, join in enumerate(result.joins):
            print(f"   Join {i+1}: {join.left_table} {join.join_type} {join.right_table} ON {join.condition}")
        
        # Test cross-procedure analysis
        print(f"\n🔄 Testing Cross-Procedure Analysis...")
        procedures = [result]
        cross_analysis = parser.analyze_cross_procedure_patterns(procedures)
        
        # Check table consolidation for BSEG
        table_consolidation = cross_analysis.get('table_consolidation', {})
        if 'BSEG' in table_consolidation:
            print(f"\n📋 BSEG Table Consolidation:")
            bseg_data = table_consolidation['BSEG']
            print(f"   Used in procedures: {bseg_data['used_in_procedures']}")
            print(f"   All columns: {bseg_data['all_columns']}")
            print(f"   Migration priority: {bseg_data['migration_priority']}")
            
            # Check filter consolidation
            filter_info = bseg_data['consolidated_filters']
            if filter_info['has_filters']:
                print(f"   Filter patterns:")
                for pattern in filter_info['patterns']:
                    print(f"     - {pattern['column']}: {pattern['unique_values']} ({pattern['migration_note']})")
            else:
                print(f"   No filters found")
        else:
            print(f"\n❌ BSEG NOT found in table consolidation!")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced ANTLR test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run enhanced ANTLR test"""
    print("🧪 Enhanced ANTLR Test")
    print("=" * 30)
    
    if test_enhanced_antlr():
        print("\n✅ Enhanced ANTLR test completed!")
        print("🚀 BSEG should now be properly parsed with all columns")
    else:
        print("\n❌ Enhanced ANTLR test failed!")

if __name__ == "__main__":
    main()
