#!/usr/bin/env python3
"""
SAP HANA Table-by-Table Analysis
================================

Extracts table usage with ONE ROW PER TABLE in the exact format requested:
- Each table gets its own row
- All columns, filters, joins for that table in one row
"""

def analyze_procedure_by_table():
    """Analyze procedure and return one row per table"""
    
    procedure_id = "PR_GS_O2C_ADDRC_DELTA"
    
    # One row per table with all details for that table
    table_analysis = [
        {
            'Procedure ID': procedure_id,
            'Table Name': 'CDHDR',
            'Column Name': 'CDHDR.MANDANT, CDHDR.OBJECTCLAS, CDHDR.OBJECTID, CDHDR.CHANGENR, CDHDR.UDATE, CDHDR.UTIME, CDHDR.USERNAME, CDHDR.TCODE',
            'Used As': 'SELECT, JOIN',
            'Filter Condition': 'CDHDR.OBJECTCLAS = \'BELEG\'',
            'Join Condition': 'CDPOS.MANDANT = CDHDR.MANDANT AND CDPOS.OBJECTCLAS = CDHDR.OBJECTCLAS AND CDPOS.OBJECTID = CDHDR.OBJECTID AND CDPOS.CHANGENR = CDHDR.CHANGENR',
            'Notes': 'Change document header - tracks all changes made to business documents'
        },
        {
            'Procedure ID': procedure_id,
            'Table Name': 'CDPOS',
            'Column Name': 'CDPOS.MANDANT, CDPOS.OBJECTCLAS, CDPOS.OBJECTID, CDPOS.CHANGENR, CDPOS.FNAME, CDPOS.TABNAME, CDPOS.VALUE_OLD, CDPOS.VALUE_NEW, CDPOS.TABKEY',
            'Used As': 'SELECT, JOIN',
            'Filter Condition': 'CDPOS.FNAME = \'RSTGR\' AND CDPOS.TABNAME = \'BSEG\'',
            'Join Condition': 'CDPOS.TABKEY = BSEG_INV.MANDT || BSEG_INV.BUKRS || BSEG_INV.BELNR || BSEG_INV.GJAHR || BSEG_INV.BUZEI',
            'Notes': 'Change document items - detailed field-level changes for dispute reason codes'
        },
        {
            'Procedure ID': procedure_id,
            'Table Name': 'BSEG',
            'Column Name': 'BSEG_INV.MANDT, BSEG_INV.BUKRS, BSEG_INV.BELNR, BSEG_INV.GJAHR, BSEG_INV.BUZEI, BSEG_INV.BSCHL, BSEG_INV.KOART',
            'Used As': 'JOIN',
            'Filter Condition': 'BSEG_INV.BSCHL IN (\'01\',\'11\') AND BSEG_INV.KOART = \'D\'',
            'Join Condition': 'BSEG_INV.MANDT = CDPOS.MANDANT AND BSEG_INV.MANDT = BKPF_INV.MANDT AND BSEG_INV.BUKRS = BKPF_INV.BUKRS AND BSEG_INV.BELNR = BKPF_INV.BELNR AND BSEG_INV.GJAHR = BKPF_INV.GJAHR',
            'Notes': 'Accounting document line items - customer line items for invoice documents'
        },
        {
            'Procedure ID': procedure_id,
            'Table Name': 'BKPF',
            'Column Name': 'BKPF_INV.MANDT, BKPF_INV.BUKRS, BKPF_INV.BELNR, BKPF_INV.GJAHR, BKPF_INV.AWTYP, BKPF_INV.AWKEY',
            'Used As': 'SELECT, JOIN',
            'Filter Condition': 'BKPF_INV.AWTYP = \'VBRK\'',
            'Join Condition': 'VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY',
            'Notes': 'Accounting document header - links accounting docs to billing documents'
        },
        {
            'Procedure ID': procedure_id,
            'Table Name': 'VBRK',
            'Column Name': 'VBRK.MANDT, VBRK.VBELN',
            'Used As': 'JOIN',
            'Filter Condition': '',
            'Join Condition': 'VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN',
            'Notes': 'Billing document header - invoice/credit memo headers'
        },
        {
            'Procedure ID': procedure_id,
            'Table Name': 'VBRP',
            'Column Name': 'VBRP.MANDT, VBRP.VBELN, VBRP.AUBEL, VBRP.AUPOS',
            'Used As': 'JOIN',
            'Filter Condition': '',
            'Join Condition': 'VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR',
            'Notes': 'Billing document items - links billing items back to sales orders'
        },
        {
            'Procedure ID': procedure_id,
            'Table Name': 'VBAP',
            'Column Name': 'VBAP.MANDT, VBAP.VBELN, VBAP.POSNR',
            'Used As': 'SELECT, JOIN',
            'Filter Condition': '',
            'Join Condition': 'VBAK.VBELN = VBAP.VBELN',
            'Notes': 'Sales document items - individual line items in sales orders'
        },
        {
            'Procedure ID': procedure_id,
            'Table Name': 'VBAK',
            'Column Name': 'VBAK.VBELN, VBAK.VBTYP',
            'Used As': 'JOIN',
            'Filter Condition': 'VBAK.VBTYP = \'C\'',
            'Join Condition': 'VBAK.VBELN = VBAP.VBELN',
            'Notes': 'Sales document header - sales order headers (type C = standard orders)'
        },
        {
            'Procedure ID': procedure_id,
            'Table Name': 'USR02',
            'Column Name': 'USR02.MANDT, USR02.BNAME, USR02.USTYP',
            'Used As': 'SELECT, LEFT JOIN',
            'Filter Condition': '',
            'Join Condition': 'USR02.MANDT = CDHDR.MANDANT AND USR02.BNAME = CDHDR.USERNAME',
            'Notes': 'User master record - current user information for change tracking'
        },
        {
            'Procedure ID': procedure_id,
            'Table Name': 'USH02',
            'Column Name': 'USH02.MANDT, USH02.BNAME, USH02.USTYP',
            'Used As': 'SELECT, LEFT JOIN',
            'Filter Condition': '',
            'Join Condition': 'USH02.MANDT = CDHDR.MANDANT AND USH02.BNAME = CDHDR.USERNAME',
            'Notes': 'User master record (historical) - historical user data for audit trail'
        },
        {
            'Procedure ID': procedure_id,
            'Table Name': 'CT_CEL_O2C_CHECKSUM_TABLE',
            'Column Name': 'CHKSUM.PARTITION_ID, CHKSUM.DATE',
            'Used As': 'SELECT, LEFT JOIN',
            'Filter Condition': 'CHKSUM.DATE = :IP_ERDAT',
            'Join Condition': '',
            'Notes': 'Checksum table for delta processing - controls incremental data loads'
        },
        {
            'Procedure ID': procedure_id,
            'Table Name': 'CT_S_AD_USER_ATTRIBUTES_EXT',
            'Column Name': 'USER_AT.EXTENSIONATTRIBUTE10, USER_AT.DEPARTMENT',
            'Used As': 'SELECT, LEFT JOIN',
            'Filter Condition': 'department <> \'\' AND EXTENSIONATTRIBUTE10 <> \'\'',
            'Join Condition': 'USER_AT.EXTENSIONATTRIBUTE10 = CDHDR.USERNAME',
            'Notes': 'User attributes extension table - additional user department information'
        }
    ]
    
    return table_analysis

def print_table_format(data):
    """Print in the exact format requested"""
    
    print("Table Usage Analysis - One Row Per Table")
    print("=" * 120)
    
    # Headers
    headers = ['Procedure ID', 'Table Name', 'Column Name', 'Used As', 'Filter Condition', 'Join Condition', 'Notes']
    
    # Print tab-separated for easy copy-paste
    print('\t'.join(headers))
    print('-' * 150)
    
    for row in data:
        values = [str(row[header]) for header in headers]
        print('\t'.join(values))

def print_markdown_format(data):
    """Print in markdown table format"""
    
    print("\n\nMarkdown Format:")
    print("=" * 40)
    
    headers = ['Procedure ID', 'Table Name', 'Column Name', 'Used As', 'Filter Condition', 'Join Condition', 'Notes']
    
    # Print markdown table
    print('| ' + ' | '.join(headers) + ' |')
    print('|' + '|'.join(['---' for _ in headers]) + '|')
    
    for row in data:
        values = [str(row[header]).replace('|', '\\|') for header in headers]
        print('| ' + ' | '.join(values) + ' |')

def save_to_csv(data, filename):
    """Save to CSV file"""
    import csv
    
    headers = ['Procedure ID', 'Table Name', 'Column Name', 'Used As', 'Filter Condition', 'Join Condition', 'Notes']
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()
            writer.writerows(data)
        return True
    except Exception as e:
        print(f"Could not save CSV: {e}")
        return False

def main():
    """Main function"""
    print("🔍 SAP HANA Table-by-Table Analysis")
    print("📋 Format: ONE ROW PER TABLE")
    print("=" * 60)
    
    # Get the analysis
    table_data = analyze_procedure_by_table()
    
    print(f"📊 Procedure: PR_GS_O2C_ADDRC_DELTA")
    print(f"📋 Tables analyzed: {len(table_data)}")
    print()
    
    # Print in table format
    print_table_format(table_data)
    
    # Save to CSV
    if save_to_csv(table_data, "table_by_table_analysis.csv"):
        print(f"\n✅ Results saved to: table_by_table_analysis.csv")
    
    # Print markdown format
    print_markdown_format(table_data)
    
    print(f"\n🎯 Analysis Summary:")
    print(f"   - Each table has its own row")
    print(f"   - All columns for that table listed together")
    print(f"   - Exact filter and join conditions shown")
    print(f"   - Business purpose explained in notes")
    
    print(f"\n📝 Copy the tab-separated format above directly into Excel!")

if __name__ == "__main__":
    main()
