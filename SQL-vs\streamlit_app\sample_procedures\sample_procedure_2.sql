PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_GS_O2C_CUSTOMER_DELTA" ( IN IP_ERDAT NVARCHAR(8) )
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
/****************************
CR/Project : 580824/ Celonis_OTC_DELTA
Purpose    : Customer master data extraction for OTC process
Activity   : Customer data synchronization
*****************************/
DECLARE DATE_VALUE NVARCHAR(10);
DECLARE TIME_VALUE NVARCHAR(10);

-- Exit handler to catch error message, if any.
DECLARE EXIT HANDLER FOR SQLEXCEPTION	
BEGIN   
    -- Display error message, if any.
	SELECT (::SQL_ERROR_CODE || ' :' || ::SQL_ERROR_MESSAGE) FROM DUMMY;		
END;

select top 1 MAX(DATE) INTO DATE_VALUE
from "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CEL_O2C_DELTA_LOG_TABLE"
where PROC_NAME = 'CUSTOMER';

select top 1 END_TIME INTO TIME_VALUE
from "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CEL_O2C_DELTA_LOG_TABLE"
where PROC_NAME = 'CUSTOMER' AND DATE = :DATE_VALUE
order by date desc;

-- Insert customer master data
INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CEL_O2C_CUSTOMER_MASTER" (
"MANDT",
"KUNNR",
"NAME1",
"NAME2", 
"STRAS",
"ORT01",
"PSTLZ",
"LAND1",
"SPRAS",
"KTOKD",
"ERDAT",
"ERNAM",
"LOEVM",
"SPERR",
"AUFSD",
"FAKSD",
"LIFSD",
"SYSTEM_ID",
"LAST_TS",
"LAST_ACTION_CD",
"LAST_ACTION_BY_ID")
SELECT DISTINCT
KNA1.MANDT AS "MANDT",
KNA1.KUNNR AS "KUNNR", 
KNA1.NAME1 AS "NAME1",
KNA1.NAME2 AS "NAME2",
KNA1.STRAS AS "STRAS",
KNA1.ORT01 AS "ORT01",
KNA1.PSTLZ AS "PSTLZ",
KNA1.LAND1 AS "LAND1",
KNA1.SPRAS AS "SPRAS",
KNA1.KTOKD AS "KTOKD",
KNA1.ERDAT AS "ERDAT",
KNA1.ERNAM AS "ERNAM",
KNA1.LOEVM AS "LOEVM",
KNA1.SPERR AS "SPERR",
KNA1.AUFSD AS "AUFSD",
KNA1.FAKSD AS "FAKSD",
KNA1.LIFSD AS "LIFSD",
'1001' AS SYSTEM_ID,
CURRENT_UTCTIMESTAMP AS LAST_TS,
'C' AS LAST_ACTION_CD,
SESSION_USER AS LAST_ACTION_BY_ID
FROM KNA1 AS KNA1
LEFT JOIN KNVV AS KNVV ON 1=1
AND KNVV.MANDT = KNA1.MANDT
AND KNVV.KUNNR = KNA1.KUNNR
AND KNVV.VKORG = '1000'
AND KNVV.VTWEG = '10'
LEFT JOIN CDHDR AS CDHDR_CUST ON 1=1
AND CDHDR_CUST.MANDANT = KNA1.MANDT
AND CDHDR_CUST.OBJECTCLAS = 'DEBI'
AND CDHDR_CUST.OBJECTID = KNA1.KUNNR
WHERE KNA1.KTOKD IN ('0001', '0002', '0003', '0004')
AND KNA1.LOEVM = ''
AND (KNA1.ERDAT >= :IP_ERDAT 
     OR (CDHDR_CUST.UDATE >= :IP_ERDAT AND CDHDR_CUST.UDATE IS NOT NULL));

-- Insert customer sales data
INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CEL_O2C_CUSTOMER_SALES" (
"MANDT",
"KUNNR",
"VKORG",
"VTWEG",
"SPART",
"KDGRP",
"BZIRK",
"KONDA",
"PLTYP",
"AWAHR",
"KALKS",
"VERSG",
"ANTLF",
"SYSTEM_ID",
"LAST_TS",
"LAST_ACTION_CD",
"LAST_ACTION_BY_ID")
SELECT DISTINCT
KNVV.MANDT AS "MANDT",
KNVV.KUNNR AS "KUNNR",
KNVV.VKORG AS "VKORG", 
KNVV.VTWEG AS "VTWEG",
KNVV.SPART AS "SPART",
KNVV.KDGRP AS "KDGRP",
KNVV.BZIRK AS "BZIRK",
KNVV.KONDA AS "KONDA",
KNVV.PLTYP AS "PLTYP",
KNVV.AWAHR AS "AWAHR",
KNVV.KALKS AS "KALKS",
KNVV.VERSG AS "VERSG",
KNVV.ANTLF AS "ANTLF",
'1001' AS SYSTEM_ID,
CURRENT_UTCTIMESTAMP AS LAST_TS,
'C' AS LAST_ACTION_CD,
SESSION_USER AS LAST_ACTION_BY_ID
FROM KNVV AS KNVV
INNER JOIN KNA1 AS KNA1 ON 1=1
AND KNA1.MANDT = KNVV.MANDT
AND KNA1.KUNNR = KNVV.KUNNR
AND KNA1.KTOKD IN ('0001', '0002', '0003', '0004')
AND KNA1.LOEVM = ''
WHERE KNVV.VKORG IN ('1000', '2000', '3000')
AND KNVV.VTWEG IN ('10', '20')
AND KNVV.SPART IN ('01', '02', '03');

INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CEL_O2C_DELTA_LOG_TABLE" 
VALUES ('O2C','CUSTOMER',CURRENT_DATE, CURRENT_UTCTIME,'Delta load');

END;
