#!/usr/bin/env python3
"""
Extract Table Usage from SAP HANA Procedure
===========================================

Extracts table usage from your SAP HANA procedure in the exact format requested.
"""

def extract_table_usage():
    """Extract table usage and format as requested"""
    
    procedure_id = "PR_GS_O2C_ADDRC_DELTA"
    
    # Table usage data extracted from your procedure
    table_usage = [
        {
            'Procedure ID': procedure_id,
            'Column Name': 'CDHDR.MANDANT, CDHDR.OBJECTCLAS, CDHDR.OBJECTID, CDHDR.CHANGENR, CDHDR.UDATE, CDHDR.UTIME, CDHDR.USERNAME, CDHDR.TCODE',
            'Used As': 'SELECT, JOIN',
            'Filter Condition': 'CDHDR.OBJECTCLAS = \'BELEG\'',
            'Join Condition': 'CDPOS.MANDANT = CDHDR.MANDANT AND CDPOS.OBJECTCLAS = CDHDR.OBJECTCLAS AND CDPOS.OBJECTID = CDHDR.OBJECTID AND CDPOS.CHANGENR = CDHDR.CHANGENR',
            'Notes': 'Change document header'
        },
        {
            'Procedure ID': procedure_id,
            'Column Name': 'CDPOS.MANDANT, CDPOS.OBJECTCLAS, CDPOS.OBJECTID, CDPOS.CHANGENR, CDPOS.FNAME, CDPOS.TABNAME, CDPOS.VALUE_OLD, CDPOS.VALUE_NEW, CDPOS.TABKEY',
            'Used As': 'SELECT, JOIN',
            'Filter Condition': 'CDPOS.FNAME = \'RSTGR\' AND CDPOS.TABNAME = \'BSEG\'',
            'Join Condition': 'CDPOS.TABKEY = BSEG_INV.MANDT || BSEG_INV.BUKRS || BSEG_INV.BELNR || BSEG_INV.GJAHR || BSEG_INV.BUZEI',
            'Notes': 'Change document items'
        },
        {
            'Procedure ID': procedure_id,
            'Column Name': 'BSEG_INV.MANDT, BSEG_INV.BUKRS, BSEG_INV.BELNR, BSEG_INV.GJAHR, BSEG_INV.BUZEI, BSEG_INV.BSCHL, BSEG_INV.KOART',
            'Used As': 'JOIN',
            'Filter Condition': 'BSEG_INV.BSCHL IN (\'01\',\'11\') AND BSEG_INV.KOART = \'D\'',
            'Join Condition': 'BSEG_INV.MANDT = CDPOS.MANDANT AND BSEG_INV.MANDT = BKPF_INV.MANDT AND BSEG_INV.BUKRS = BKPF_INV.BUKRS AND BSEG_INV.BELNR = BKPF_INV.BELNR AND BSEG_INV.GJAHR = BKPF_INV.GJAHR',
            'Notes': 'Accounting document line items'
        },
        {
            'Procedure ID': procedure_id,
            'Column Name': 'BKPF_INV.MANDT, BKPF_INV.BUKRS, BKPF_INV.BELNR, BKPF_INV.GJAHR, BKPF_INV.AWTYP, BKPF_INV.AWKEY',
            'Used As': 'SELECT, JOIN',
            'Filter Condition': 'BKPF_INV.AWTYP = \'VBRK\'',
            'Join Condition': 'VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY',
            'Notes': 'Accounting document header'
        },
        {
            'Procedure ID': procedure_id,
            'Column Name': 'VBRK.MANDT, VBRK.VBELN',
            'Used As': 'JOIN',
            'Filter Condition': '',
            'Join Condition': 'VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN',
            'Notes': 'Billing document header'
        },
        {
            'Procedure ID': procedure_id,
            'Column Name': 'VBRP.MANDT, VBRP.VBELN, VBRP.AUBEL, VBRP.AUPOS',
            'Used As': 'JOIN',
            'Filter Condition': '',
            'Join Condition': 'VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR',
            'Notes': 'Billing document items'
        },
        {
            'Procedure ID': procedure_id,
            'Column Name': 'VBAP.MANDT, VBAP.VBELN, VBAP.POSNR',
            'Used As': 'SELECT, JOIN',
            'Filter Condition': '',
            'Join Condition': 'VBAK.VBELN = VBAP.VBELN',
            'Notes': 'Sales document items'
        },
        {
            'Procedure ID': procedure_id,
            'Column Name': 'VBAK.VBELN, VBAK.VBTYP',
            'Used As': 'JOIN',
            'Filter Condition': 'VBAK.VBTYP = \'C\'',
            'Join Condition': 'VBAK.VBELN = VBAP.VBELN',
            'Notes': 'Sales document header'
        },
        {
            'Procedure ID': procedure_id,
            'Column Name': 'USR02.MANDT, USR02.BNAME, USR02.USTYP',
            'Used As': 'SELECT, LEFT JOIN',
            'Filter Condition': '',
            'Join Condition': 'USR02.MANDT = CDHDR.MANDANT AND USR02.BNAME = CDHDR.USERNAME',
            'Notes': 'User master record'
        },
        {
            'Procedure ID': procedure_id,
            'Column Name': 'USH02.MANDT, USH02.BNAME, USH02.USTYP',
            'Used As': 'SELECT, LEFT JOIN',
            'Filter Condition': '',
            'Join Condition': 'USH02.MANDT = CDHDR.MANDANT AND USH02.BNAME = CDHDR.USERNAME',
            'Notes': 'User master record (historical)'
        },
        {
            'Procedure ID': procedure_id,
            'Column Name': 'CHKSUM.PARTITION_ID, CHKSUM.DATE',
            'Used As': 'SELECT, LEFT JOIN',
            'Filter Condition': 'CHKSUM.DATE = :IP_ERDAT',
            'Join Condition': '',
            'Notes': 'Checksum table for delta processing'
        },
        {
            'Procedure ID': procedure_id,
            'Column Name': 'USER_AT.EXTENSIONATTRIBUTE10, USER_AT.DEPARTMENT',
            'Used As': 'SELECT, LEFT JOIN',
            'Filter Condition': 'department <> \'\' AND EXTENSIONATTRIBUTE10 <> \'\'',
            'Join Condition': 'USER_AT.EXTENSIONATTRIBUTE10 = CDHDR.USERNAME',
            'Notes': 'User attributes extension table'
        }
    ]
    
    return table_usage

def format_table(data):
    """Format data as a table"""
    
    # Calculate column widths
    headers = ['Procedure ID', 'Column Name', 'Used As', 'Filter Condition', 'Join Condition', 'Notes']
    
    # Calculate max width for each column
    col_widths = []
    for i, header in enumerate(headers):
        max_width = len(header)
        for row in data:
            cell_value = str(list(row.values())[i])
            if len(cell_value) > max_width:
                max_width = min(len(cell_value), 80)  # Limit to 80 chars
        col_widths.append(max_width + 2)
    
    # Create separator line
    separator = '+' + '+'.join(['-' * width for width in col_widths]) + '+'
    
    # Print table
    print(separator)
    
    # Print header
    header_row = '|'
    for i, header in enumerate(headers):
        header_row += f" {header:<{col_widths[i]-1}}|"
    print(header_row)
    print(separator)
    
    # Print data rows
    for row in data:
        data_row = '|'
        for i, value in enumerate(row.values()):
            # Truncate long values
            cell_value = str(value)
            if len(cell_value) > col_widths[i] - 2:
                cell_value = cell_value[:col_widths[i]-5] + '...'
            data_row += f" {cell_value:<{col_widths[i]-1}}|"
        print(data_row)
    
    print(separator)

def save_as_csv(data, filename):
    """Save data as CSV"""
    import csv
    
    headers = ['Procedure ID', 'Column Name', 'Used As', 'Filter Condition', 'Join Condition', 'Notes']
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=headers)
        writer.writeheader()
        writer.writerows(data)

def main():
    """Main function"""
    print("SAP HANA Procedure Table Usage Analysis")
    print("=" * 60)
    print("Procedure: PR_GS_O2C_ADDRC_DELTA")
    print("=" * 60)
    
    # Extract table usage
    table_data = extract_table_usage()
    
    # Format and display table
    format_table(table_data)
    
    # Save as CSV
    csv_filename = "analysis_output/table_usage_analysis.csv"
    try:
        save_as_csv(table_data, csv_filename)
        print(f"\n✅ Results saved to: {csv_filename}")
    except Exception as e:
        print(f"\n⚠️ Could not save CSV: {e}")
    
    print("\n📋 Copy-paste ready format:")
    print("=" * 40)
    
    # Print in simple tab-separated format for easy copying
    headers = ['Procedure ID', 'Column Name', 'Used As', 'Filter Condition', 'Join Condition', 'Notes']
    print('\t'.join(headers))
    
    for row in table_data:
        values = [str(row[header]) for header in headers]
        print('\t'.join(values))
    
    print(f"\n📊 Total tables analyzed: {len(table_data)}")
    print("🎯 Ready for requirements documentation!")

if __name__ == "__main__":
    main()
