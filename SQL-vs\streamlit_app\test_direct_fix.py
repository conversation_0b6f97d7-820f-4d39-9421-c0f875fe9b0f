#!/usr/bin/env python3
"""
Test Direct Fix
===============

Test a direct fix for column extraction.
"""

import re
from collections import defaultdict

sql = '''
SELECT 
    h.bukrs as company_code,
    h.belnr as document_number,
    h.bldat as document_date,
    i.lifnr as vendor_number,
    i.dmbtr as amount_local_currency,
    i.koart as account_type
FROM bkpf h
INNER JOIN bseg i ON h.belnr = i.belnr 
WHERE h.bukrs = 'US01'
  AND i.koart = 'K'
  AND i.lifnr IS NOT NULL;
'''

def extract_columns_direct(sql_text: str):
    """Direct column extraction that should work"""
    
    # Step 1: Build alias mapping
    alias_to_table = {}
    alias_patterns = [
        r'FROM\s+([a-zA-Z][a-zA-Z0-9_]{2,7})\s+(?:AS\s+)?(\w+)',
        r'JOIN\s+([a-zA-Z][a-zA-Z0-9_]{2,7})\s+(?:AS\s+)?(\w+)',
        r'INNER\s+JOIN\s+([a-zA-Z][a-zA-Z0-9_]{2,7})\s+(?:AS\s+)?(\w+)',
    ]
    
    for pattern in alias_patterns:
        matches = re.finditer(pattern, sql_text, re.IGNORECASE)
        for match in matches:
            table_name = match.group(1).upper()
            alias = match.group(2).upper()
            if alias != table_name and len(alias) <= 3:
                alias_to_table[alias] = table_name
    
    print(f"Alias mapping: {alias_to_table}")
    
    # Step 2: Find all table.column patterns
    column_pattern = r'(\w+)\.(\w+)'
    matches = re.findall(column_pattern, sql_text, re.IGNORECASE)
    
    print(f"All table.column patterns: {matches}")
    
    # Step 3: Group by actual table (resolve aliases)
    table_columns = defaultdict(set)
    
    for table_or_alias, column in matches:
        table_or_alias_upper = table_or_alias.upper()
        column_upper = column.upper()
        
        # Resolve alias to actual table
        actual_table = alias_to_table.get(table_or_alias_upper, table_or_alias_upper)
        table_columns[actual_table].add(column_upper)
    
    return dict(table_columns)

# Test the direct method
print("🔍 Testing Direct Column Extraction")
print("=" * 40)

result = extract_columns_direct(sql)
print(f"\nResult: {result}")

for table, columns in result.items():
    print(f"{table}: {sorted(list(columns))}")

# Now test if this works in the parser
print(f"\n🔧 Testing in Parser")
from sql_parser import AdvancedSQLParser

parser = AdvancedSQLParser()

# Override the method temporarily
def fixed_extract_columns_for_table(self, sql_text: str, table_name: str):
    """Fixed column extraction"""
    
    # Build alias mapping
    alias_to_table = self._build_alias_mapping(sql_text)
    
    # Find all table.column patterns
    column_pattern = r'(\w+)\.(\w+)'
    matches = re.findall(column_pattern, sql_text, re.IGNORECASE)
    
    # Find columns for this specific table
    columns = set()
    for table_or_alias, column in matches:
        table_or_alias_upper = table_or_alias.upper()
        column_upper = column.upper()
        
        # Check if this is the table we want (direct or via alias)
        if table_or_alias_upper == table_name:
            columns.add(column_upper)
        elif table_or_alias_upper in alias_to_table and alias_to_table[table_or_alias_upper] == table_name:
            columns.add(column_upper)
    
    return columns

# Monkey patch the method
parser._extract_columns_for_table = lambda sql_text, table_name: fixed_extract_columns_for_table(parser, sql_text, table_name)

# Test with the fixed method
result = parser.parse_procedure(sql, "test_fixed")
print(f"\nFixed parsing results:")
print(f"   Tables: {list(result.tables.keys())}")
for table_name, table_ref in result.tables.items():
    print(f"   {table_name} columns: {sorted(list(table_ref.columns))}")

print(f"   Filters: {len(result.filters)}")
for filter_cond in result.filters:
    print(f"     {filter_cond.table}.{filter_cond.column} {filter_cond.operator} {filter_cond.values}")

print(f"\n✅ BSEG columns should now include: LIFNR, DMBTR, KOART")
print(f"✅ BKPF columns should now include: BUKRS, BELNR, BLDAT")
