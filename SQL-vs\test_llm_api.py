#!/usr/bin/env python3
"""
Test LLM API Connection
=======================

This script tests the Azure OpenAI API connection using your exact configuration.
"""

import json
from pathlib import Path

# Try different OpenAI client approaches
def test_openai_approaches():
    """Test different ways to initialize OpenAI client"""
    
    # Your API configuration
    endpoint = "https://models.inference.ai.azure.com"
    model = "gpt-4o"
    token = "****************************************"
    
    print("Testing Azure OpenAI API Connection")
    print("=" * 40)
    
    # Approach 1: Standard OpenAI client
    try:
        from openai import OpenAI
        
        client = OpenAI(
            base_url=endpoint,
            api_key=token,
        )
        
        print("✅ OpenAI client initialized successfully")
        
        # Test API call
        response = client.chat.completions.create(
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say 'Hello from SAP HANA analysis!' if you can respond."}
            ],
            model=model,
            max_tokens=100,
            temperature=0.7
        )
        
        print("✅ API call successful!")
        print(f"Response: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ Approach 1 failed: {e}")
    
    # Approach 2: Using requests directly
    try:
        import requests
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Say 'Hello from SAP HANA analysis!' if you can respond."}
            ],
            "max_tokens": 100,
            "temperature": 0.7
        }
        
        response = requests.post(
            f"{endpoint}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Direct API call successful!")
            print(f"Response: {result['choices'][0]['message']['content']}")
            return True
        else:
            print(f"❌ API call failed with status {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"❌ Approach 2 failed: {e}")
    
    return False

def generate_sample_requirements():
    """Generate sample requirements using the working API approach"""
    
    # Load analysis data
    analysis_file = Path("analysis_output/table_catalog.json")
    if not analysis_file.exists():
        print("❌ Analysis data not found. Please run the complete analysis first.")
        return
    
    with open(analysis_file, 'r') as f:
        table_data = json.load(f)
    
    # Your API configuration
    endpoint = "https://models.inference.ai.azure.com"
    model = "gpt-4o"
    token = "****************************************"
    
    try:
        import requests
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # Create a focused prompt with the table data
        tables_summary = table_data.get('summary', {})
        key_tables = table_data.get('tables', {})
        
        prompt = f"""Based on this SAP HANA table analysis, generate comprehensive business requirements:

## Table Summary
- Total tables: {tables_summary.get('total_tables', 0)}
- Most used tables: {tables_summary.get('most_used_tables', [])}

## Key Tables Identified:
{json.dumps(key_tables, indent=2)}

Please generate:
1. Executive Summary
2. Key Business Processes
3. Functional Requirements
4. Data Integration Requirements

Focus on SAP ERP business context and make it suitable for stakeholders."""
        
        data = {
            "model": model,
            "messages": [
                {
                    "role": "system", 
                    "content": "You are a senior SAP business analyst. Generate comprehensive business requirements documentation based on SQL table analysis. Write in clear, business-friendly language."
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "max_tokens": 2000,
            "temperature": 0.7
        }
        
        print("🤖 Generating LLM-powered business requirements...")
        
        response = requests.post(
            f"{endpoint}/chat/completions",
            headers=headers,
            json=data,
            timeout=60
        )
        
        if response.status_code == 200:
            result = response.json()
            requirements = result['choices'][0]['message']['content']
            
            # Save the generated requirements
            output_dir = Path("analysis_output/llm_generated")
            output_dir.mkdir(exist_ok=True)
            
            output_file = output_dir / "llm_business_requirements.md"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(requirements)
            
            print("✅ LLM-powered business requirements generated!")
            print(f"📁 Saved to: {output_file}")
            print("\n📄 Preview:")
            print("=" * 50)
            print(requirements[:500] + "..." if len(requirements) > 500 else requirements)
            
            return True
            
        else:
            print(f"❌ API call failed with status {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error generating requirements: {e}")
        return False

def main():
    """Main function"""
    print("🧪 Testing LLM API and Generating Sample Requirements")
    print("=" * 60)
    
    # Test API connection
    if test_openai_approaches():
        print("\n🎯 API connection successful! Generating sample requirements...")
        generate_sample_requirements()
    else:
        print("\n❌ API connection failed. Please check:")
        print("1. API token is valid")
        print("2. Internet connection is working")
        print("3. Azure OpenAI service is accessible")

if __name__ == "__main__":
    main()
