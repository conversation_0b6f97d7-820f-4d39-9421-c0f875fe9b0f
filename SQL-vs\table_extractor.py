#!/usr/bin/env python3
"""
SAP HANA Table Extractor
========================

Extracts table references from SAP HANA procedures and formats them in the exact format requested:
- Procedure ID
- Column Name  
- Used As
- Filter Condition
- Join Condition
- Notes
"""

import re
import pandas as pd
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from dataclasses import dataclass

@dataclass
class TableUsage:
    """Represents how a table is used in a procedure"""
    procedure_id: str
    table_name: str
    columns: List[str]
    used_as: List[str]  # SELECT, JOIN, WHERE, etc.
    filter_conditions: List[str]
    join_conditions: List[str]
    notes: str

class SAP_HANA_TableExtractor:
    """Extract table usage information from SAP HANA procedures"""
    
    def __init__(self):
        self.table_usages = []
        
        # Common SAP table patterns and their purposes
        self.table_purposes = {
            'VBAK': 'Sales document header',
            'VBAP': 'Sales document items',
            'VBFA': 'Sales document flow',
            'VBRK': 'Billing document header',
            'VBRP': 'Billing document items',
            'BKPF': 'Accounting document header',
            'BSEG': 'Accounting document line items',
            'CDHDR': 'Change document header',
            'CDPOS': 'Change document items',
            'USR02': 'User master record',
            'USH02': 'User master record (historical)',
            'KNA1': 'Customer master',
            'LFA1': 'Vendor master',
            'MARA': 'Material master',
            'MARD': 'Material stock data',
            'MAKT': 'Material descriptions'
        }
    
    def extract_from_procedure(self, procedure_text: str, procedure_id: str = None) -> List[TableUsage]:
        """Extract table usage from a single procedure"""
        
        # Extract procedure name if not provided
        if not procedure_id:
            proc_match = re.search(r'PROCEDURE\s+"([^"]+)"', procedure_text, re.IGNORECASE)
            if proc_match:
                procedure_id = proc_match.group(1).split('::')[-1]  # Get last part after ::
            else:
                procedure_id = "UNKNOWN_PROC"
        
        # Clean the procedure text
        cleaned_text = self._clean_sql_text(procedure_text)
        
        # Extract table references
        table_usages = []
        
        # Find all table references in different contexts
        tables_found = self._find_all_tables(cleaned_text)
        
        for table_name in tables_found:
            usage = self._analyze_table_usage(cleaned_text, table_name, procedure_id)
            if usage:
                table_usages.append(usage)
        
        return table_usages
    
    def _clean_sql_text(self, text: str) -> str:
        """Clean SQL text for better parsing"""
        # Remove comments
        text = re.sub(r'/\*.*?\*/', '', text, flags=re.DOTALL)
        text = re.sub(r'--.*?\n', '\n', text)
        
        # Remove string literals to avoid false matches
        text = re.sub(r"'[^']*'", "'STRING'", text)
        text = re.sub(r'"[^"]*"', '"STRING"', text)
        
        return text
    
    def _find_all_tables(self, text: str) -> Set[str]:
        """Find all table references in the SQL text"""
        tables = set()
        
        # Pattern 1: Standard table references (schema.table or just table)
        # Look for patterns like: FROM table, JOIN table, INTO table, etc.
        table_patterns = [
            r'\bFROM\s+(?:"[^"]+"\.)?"?([A-Z][A-Z0-9_]+)"?\s',
            r'\bJOIN\s+(?:"[^"]+"\.)?"?([A-Z][A-Z0-9_]+)"?\s',
            r'\bINTO\s+(?:"[^"]+"\.)?"?([A-Z][A-Z0-9_]+)"?\s',
            r'\bUPDATE\s+(?:"[^"]+"\.)?"?([A-Z][A-Z0-9_]+)"?\s',
            r'\bINSERT\s+INTO\s+(?:"[^"]+"\.)?"?([A-Z][A-Z0-9_]+)"?\s',
        ]
        
        for pattern in table_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                if len(match) >= 3 and match.isalpha():  # Basic validation
                    tables.add(match.upper())
        
        # Pattern 2: Table aliases (AS alias_name)
        alias_pattern = r'\b(?:FROM|JOIN)\s+(?:"[^"]+"\.)?"?([A-Z][A-Z0-9_]+)"?\s+(?:AS\s+)?([A-Z][A-Z0-9_]+)\s'
        alias_matches = re.findall(alias_pattern, text, re.IGNORECASE)
        for table, alias in alias_matches:
            if len(table) >= 3:
                tables.add(table.upper())
        
        # Pattern 3: Direct table references in column specifications
        column_pattern = r'\b([A-Z][A-Z0-9_]+)\.([A-Z][A-Z0-9_]+)\b'
        column_matches = re.findall(column_pattern, text)
        for table, column in column_matches:
            if len(table) >= 3 and table.upper() in self.table_purposes:
                tables.add(table.upper())
        
        return tables
    
    def _analyze_table_usage(self, text: str, table_name: str, procedure_id: str) -> Optional[TableUsage]:
        """Analyze how a specific table is used in the procedure"""
        
        # Find all column references for this table
        columns = self._extract_columns_for_table(text, table_name)
        
        # Determine usage contexts
        used_as = self._determine_usage_context(text, table_name)
        
        # Extract filter conditions
        filter_conditions = self._extract_filter_conditions(text, table_name)
        
        # Extract join conditions
        join_conditions = self._extract_join_conditions(text, table_name)
        
        # Get table purpose/notes
        notes = self.table_purposes.get(table_name, 'Business data table')
        
        if columns or used_as:  # Only create usage if we found something
            return TableUsage(
                procedure_id=procedure_id,
                table_name=table_name,
                columns=columns,
                used_as=used_as,
                filter_conditions=filter_conditions,
                join_conditions=join_conditions,
                notes=notes
            )
        
        return None
    
    def _extract_columns_for_table(self, text: str, table_name: str) -> List[str]:
        """Extract all column references for a specific table"""
        columns = set()
        
        # Pattern: TABLE.COLUMN or ALIAS.COLUMN
        pattern = rf'\b{table_name}\.([A-Z][A-Z0-9_]*)\b'
        matches = re.findall(pattern, text, re.IGNORECASE)
        
        for match in matches:
            columns.add(match.upper())
        
        # Also look for table aliases
        alias_pattern = rf'\b(?:FROM|JOIN)\s+(?:"[^"]+"\.)?"?{table_name}"?\s+(?:AS\s+)?([A-Z][A-Z0-9_]+)\s'
        alias_matches = re.findall(alias_pattern, text, re.IGNORECASE)
        
        for alias in alias_matches:
            alias_pattern = rf'\b{alias}\.([A-Z][A-Z0-9_]*)\b'
            alias_column_matches = re.findall(alias_pattern, text, re.IGNORECASE)
            for col in alias_column_matches:
                columns.add(col.upper())
        
        return sorted(list(columns))
    
    def _determine_usage_context(self, text: str, table_name: str) -> List[str]:
        """Determine how the table is used (SELECT, JOIN, WHERE, etc.)"""
        contexts = set()
        
        # Check for different usage patterns
        if re.search(rf'\bSELECT\b.*?\b{table_name}\.', text, re.IGNORECASE | re.DOTALL):
            contexts.add('SELECT')
        
        if re.search(rf'\bFROM\s+(?:"[^"]+"\.)?"?{table_name}"?\s', text, re.IGNORECASE):
            contexts.add('FROM')
        
        if re.search(rf'\bJOIN\s+(?:"[^"]+"\.)?"?{table_name}"?\s', text, re.IGNORECASE):
            contexts.add('JOIN')
        
        if re.search(rf'\bWHERE\b.*?\b{table_name}\.', text, re.IGNORECASE | re.DOTALL):
            contexts.add('WHERE')
        
        if re.search(rf'\bINSERT\s+INTO\s+(?:"[^"]+"\.)?"?{table_name}"?\s', text, re.IGNORECASE):
            contexts.add('INSERT')
        
        if re.search(rf'\bUPDATE\s+(?:"[^"]+"\.)?"?{table_name}"?\s', text, re.IGNORECASE):
            contexts.add('UPDATE')
        
        return sorted(list(contexts))
    
    def _extract_filter_conditions(self, text: str, table_name: str) -> List[str]:
        """Extract WHERE clause conditions for the table"""
        conditions = []
        
        # Find WHERE clauses that reference this table
        where_pattern = rf'\bWHERE\b(.*?)(?:\bGROUP\s+BY\b|\bORDER\s+BY\b|\bHAVING\b|\bUNION\b|\bINTERSECT\b|\bEXCEPT\b|\;|\bAND\s+{table_name}\.|\bOR\s+{table_name}\.)'
        where_matches = re.findall(where_pattern, text, re.IGNORECASE | re.DOTALL)
        
        for where_clause in where_matches:
            # Look for conditions involving this table
            table_conditions = re.findall(rf'{table_name}\.([A-Z0-9_]+\s*[<>=!]+\s*[^,\s\)]+)', where_clause, re.IGNORECASE)
            for condition in table_conditions:
                conditions.append(f"{table_name}.{condition.strip()}")
        
        return conditions
    
    def _extract_join_conditions(self, text: str, table_name: str) -> List[str]:
        """Extract JOIN conditions for the table"""
        conditions = []
        
        # Find JOIN clauses involving this table
        join_pattern = rf'\bJOIN\s+(?:"[^"]+"\.)?"?{table_name}"?[^O]*?\bON\s+(.*?)(?:\bINNER\b|\bLEFT\b|\bRIGHT\b|\bFULL\b|\bJOIN\b|\bWHERE\b|\bGROUP\b|\bORDER\b|\bHAVING\b|\;)'
        join_matches = re.findall(join_pattern, text, re.IGNORECASE | re.DOTALL)
        
        for join_clause in join_matches:
            # Clean up the join condition
            clean_condition = re.sub(r'\s+', ' ', join_clause.strip())
            if clean_condition and len(clean_condition) < 200:  # Reasonable length
                conditions.append(clean_condition)
        
        return conditions
    
    def extract_from_file(self, file_path: Path) -> List[TableUsage]:
        """Extract table usage from a procedure file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            procedure_id = file_path.stem
            return self.extract_from_procedure(content, procedure_id)
            
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            return []
    
    def extract_from_directory(self, directory: Path) -> List[TableUsage]:
        """Extract table usage from all procedures in a directory"""
        all_usages = []
        
        sql_files = list(directory.glob("*.sql")) + list(directory.glob("*.hdbprocedure"))
        
        for file_path in sql_files:
            usages = self.extract_from_file(file_path)
            all_usages.extend(usages)
        
        return all_usages
    
    def format_as_table(self, usages: List[TableUsage]) -> pd.DataFrame:
        """Format the extracted data as a pandas DataFrame in the requested format"""
        
        rows = []
        for usage in usages:
            # Format columns as comma-separated string
            columns_str = ', '.join([f"{usage.table_name}.{col}" for col in usage.columns])
            
            # Format used_as as comma-separated string
            used_as_str = ', '.join(usage.used_as)
            
            # Format filter conditions
            filter_str = ' AND '.join(usage.filter_conditions) if usage.filter_conditions else ''
            
            # Format join conditions
            join_str = ' AND '.join(usage.join_conditions) if usage.join_conditions else ''
            
            rows.append({
                'Procedure ID': usage.procedure_id,
                'Table Name': usage.table_name,
                'Column Name': columns_str,
                'Used As': used_as_str,
                'Filter Condition': filter_str,
                'Join Condition': join_str,
                'Notes': usage.notes
            })
        
        return pd.DataFrame(rows)

def main():
    """Main function to demonstrate usage"""
    
    # Example usage with the provided procedure
    sample_procedure = '''
    PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_GS_O2C_ADDRC_DELTA" ( IN IP_ERDAT NVARCHAR(8) )
    LANGUAGE SQLSCRIPT
    SQL SECURITY INVOKER
    DEFAULT SCHEMA GSAP_ECC
    AS
    BEGIN
    -- Your procedure content here
    SELECT VBAP.MANDT, VBAP.VBELN, VBAP.POSNR
    FROM VBAP AS VBAP
    INNER JOIN VBAK AS VBAK ON VBAK.VBELN = VBAP.VBELN
    WHERE VBAP.ERDAT > '20230101'
    AND VBAK.VBTYP = 'C';
    END;
    '''
    
    extractor = SAP_HANA_TableExtractor()
    usages = extractor.extract_from_procedure(sample_procedure, "PR_GS_O2C_ADDRC_DELTA")
    
    if usages:
        df = extractor.format_as_table(usages)
        print("Extracted Table Usage:")
        print("=" * 80)
        print(df.to_string(index=False))
    else:
        print("No table usages found.")

if __name__ == "__main__":
    main()
