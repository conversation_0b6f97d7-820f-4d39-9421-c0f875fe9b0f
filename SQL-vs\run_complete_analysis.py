#!/usr/bin/env python3
"""
Complete SAP HANA SQL Analysis Workflow
=======================================

This script orchestrates the complete analysis workflow:
1. Setup ANTLR parser environment
2. Analyze all SQL procedures
3. Generate requirements documentation
4. Create LLM-optimized output

Usage:
    python run_complete_analysis.py
"""

import sys
import json
from pathlib import Path
from typing import List, Dict, Any

# Import our modules
from sql_parser_setup import SAP_HANA_Parser_Setup
from sql_analyzer import SQLProcedureAnalyzer
from requirements_generator import RequirementsDocumentationGenerator
from llm_requirements_generator import LLMRequirementsGenerator

class CompleteAnalysisWorkflow:
    """Orchestrates the complete analysis workflow"""
    
    def __init__(self, workspace_dir: str = "."):
        self.workspace_dir = Path(workspace_dir)
        self.procedures_dir = self.workspace_dir / "procedures"
        self.output_dir = self.workspace_dir / "analysis_output"
        
        # Initialize components
        self.parser_setup = SAP_HANA_Parser_Setup(workspace_dir)
        self.analyzer = SQLProcedureAnalyzer(
            str(self.procedures_dir), 
            str(self.output_dir)
        )
        
    def run_complete_workflow(self) -> bool:
        """Run the complete analysis workflow"""
        print("SAP HANA SQL Complete Analysis Workflow")
        print("=" * 50)
        
        # Step 1: Setup parser environment
        print("\n🔧 Step 1: Setting up parser environment...")
        if not self.parser_setup.setup_parser_environment():
            print("❌ Parser setup failed")
            return False
        
        # Step 2: Check for SQL procedures
        print("\n📁 Step 2: Checking for SQL procedures...")
        if not self._check_procedures():
            return False
        
        # Step 3: Analyze procedures
        print("\n🔍 Step 3: Analyzing SQL procedures...")
        analyses = self.analyzer.analyze_all_procedures()
        if not analyses:
            print("❌ No procedures were successfully analyzed")
            return False
        
        # Step 4: Save analysis results
        print("\n💾 Step 4: Saving analysis results...")
        analysis_data = self._convert_analyses_to_dict(analyses)
        self._save_analysis_data(analysis_data)
        
        # Step 5: Generate requirements documentation
        print("\n📋 Step 5: Generating requirements documentation...")
        doc_generator = RequirementsDocumentationGenerator(
            analysis_data, str(self.output_dir)
        )
        doc_generator.generate_all_documentation()
        
        # Step 6: Generate LLM-powered requirements documentation
        print("\n🤖 Step 6: Generating LLM-powered requirements documentation...")
        try:
            llm_generator = LLMRequirementsGenerator(str(self.output_dir))
            llm_generator.generate_all_documentation()
        except Exception as e:
            print(f"⚠️ Warning: LLM generation failed: {e}")
            print("Continuing with basic documentation...")

        # Step 7: Generate final summary
        print("\n📊 Step 7: Generating final summary...")
        self._generate_final_summary(analyses, analysis_data)

        print("\n🎉 Complete analysis workflow finished successfully!")
        return True
    
    def _check_procedures(self) -> bool:
        """Check if SQL procedures exist"""
        if not self.procedures_dir.exists():
            print(f"❌ Procedures directory not found: {self.procedures_dir}")
            print("Creating directory and adding sample procedures...")
            self._create_sample_procedures()
            return True
        
        sql_files = list(self.procedures_dir.glob("*.sql")) + \
                   list(self.procedures_dir.glob("*.SQL"))
        
        if not sql_files:
            print(f"❌ No SQL files found in {self.procedures_dir}")
            print("Adding sample procedures...")
            self._create_sample_procedures()
            return True
        
        print(f"✅ Found {len(sql_files)} SQL procedure files")
        return True
    
    def _create_sample_procedures(self):
        """Create sample SAP HANA SQL procedures for demonstration"""
        self.procedures_dir.mkdir(exist_ok=True)
        
        sample_procedures = [
            {
                'name': 'get_customer_orders.sql',
                'content': '''
CREATE PROCEDURE get_customer_orders(
    IN p_customer_id NVARCHAR(10),
    IN p_date_from DATE,
    IN p_date_to DATE
)
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
READS SQL DATA AS
BEGIN
    SELECT 
        o.client,
        o.vbeln as order_number,
        o.audat as order_date,
        o.kunnr as customer_id,
        c.name1 as customer_name,
        i.posnr as item_number,
        i.matnr as material_number,
        m.maktx as material_description,
        i.kwmeng as quantity,
        i.netwr as net_value
    FROM vbak o
    INNER JOIN vbap i ON o.vbeln = i.vbeln
    INNER JOIN kna1 c ON o.kunnr = c.kunnr
    LEFT JOIN makt m ON i.matnr = m.matnr
    WHERE o.kunnr = p_customer_id
      AND o.audat BETWEEN p_date_from AND p_date_to
      AND o.vbtyp = 'C'
    ORDER BY o.audat DESC, o.vbeln, i.posnr;
END;
'''
            },
            {
                'name': 'calculate_material_stock.sql',
                'content': '''
CREATE PROCEDURE calculate_material_stock(
    IN p_plant NVARCHAR(4),
    IN p_material_group NVARCHAR(9)
)
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
READS SQL DATA AS
BEGIN
    DECLARE v_total_stock DECIMAL(15,3);
    
    SELECT 
        s.matnr as material_number,
        m.maktx as material_description,
        s.werks as plant,
        s.lgort as storage_location,
        SUM(s.labst) as available_stock,
        SUM(s.insme) as quality_inspection_stock,
        SUM(s.speme) as blocked_stock,
        m.meins as base_unit
    FROM mard s
    INNER JOIN mara m ON s.matnr = m.matnr
    INNER JOIN makt mt ON m.matnr = mt.matnr
    WHERE s.werks = p_plant
      AND m.matkl = p_material_group
      AND s.labst > 0
    GROUP BY s.matnr, m.maktx, s.werks, s.lgort, m.meins
    ORDER BY s.matnr, s.lgort;
END;
'''
            },
            {
                'name': 'vendor_payment_analysis.sql',
                'content': '''
CREATE PROCEDURE vendor_payment_analysis(
    IN p_company_code NVARCHAR(4),
    IN p_fiscal_year NVARCHAR(4)
)
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
READS SQL DATA AS
BEGIN
    SELECT 
        h.bukrs as company_code,
        h.belnr as document_number,
        h.gjahr as fiscal_year,
        h.bldat as document_date,
        h.budat as posting_date,
        i.lifnr as vendor_number,
        v.name1 as vendor_name,
        i.dmbtr as amount_local_currency,
        i.wrbtr as amount_document_currency,
        i.waers as currency,
        i.zfbdt as baseline_date,
        i.zbd1t as cash_discount_days,
        i.zbd2t as payment_terms_days,
        CASE 
            WHEN DAYS_BETWEEN(i.zfbdt, CURRENT_DATE) <= i.zbd1t 
            THEN 'DISCOUNT_PERIOD'
            WHEN DAYS_BETWEEN(i.zfbdt, CURRENT_DATE) <= i.zbd2t 
            THEN 'PAYMENT_PERIOD'
            ELSE 'OVERDUE'
        END as payment_status
    FROM bkpf h
    INNER JOIN bseg i ON h.belnr = i.belnr 
                     AND h.gjahr = i.gjahr 
                     AND h.bukrs = i.bukrs
    INNER JOIN lfa1 v ON i.lifnr = v.lifnr
    WHERE h.bukrs = p_company_code
      AND h.gjahr = p_fiscal_year
      AND i.koart = 'K'
      AND i.lifnr IS NOT NULL
    ORDER BY h.budat DESC, i.lifnr;
END;
'''
            }
        ]
        
        for proc in sample_procedures:
            proc_file = self.procedures_dir / proc['name']
            with open(proc_file, 'w', encoding='utf-8') as f:
                f.write(proc['content'])
        
        print(f"✅ Created {len(sample_procedures)} sample procedures in {self.procedures_dir}")
    
    def _convert_analyses_to_dict(self, analyses) -> List[Dict[str, Any]]:
        """Convert analysis objects to dictionaries"""
        analysis_data = []
        for analysis in analyses:
            # Convert dataclass to dict
            analysis_dict = {
                'procedure_name': analysis.procedure_name,
                'file_path': analysis.file_path,
                'tables': [
                    {
                        'name': t.name,
                        'schema': t.schema,
                        'alias': t.alias,
                        'full_name': t.full_name
                    } for t in analysis.tables
                ],
                'columns': [
                    {
                        'name': c.name,
                        'table': c.table,
                        'table_alias': c.table_alias,
                        'context': c.context
                    } for c in analysis.columns
                ],
                'joins': [
                    {
                        'join_type': j.join_type,
                        'left_table': j.left_table,
                        'right_table': j.right_table,
                        'condition': j.condition,
                        'line_number': j.line_number
                    } for j in analysis.joins
                ],
                'where_conditions': [
                    {
                        'column': w.column,
                        'operator': w.operator,
                        'value': w.value,
                        'table': w.table,
                        'line_number': w.line_number
                    } for w in analysis.where_conditions
                ],
                'input_parameters': analysis.input_parameters,
                'output_parameters': analysis.output_parameters,
                'analysis_summary': analysis.analysis_summary
            }
            analysis_data.append(analysis_dict)
        
        return analysis_data
    
    def _save_analysis_data(self, analysis_data: List[Dict[str, Any]]):
        """Save raw analysis data"""
        output_file = self.output_dir / "raw_analysis_data.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ Raw analysis data saved to: {output_file}")
    
    def _generate_final_summary(self, analyses, analysis_data: List[Dict[str, Any]]):
        """Generate final summary report"""
        summary = {
            'workflow_summary': {
                'total_procedures_analyzed': len(analyses),
                'successful_parses': len([a for a in analyses if a is not None]),
                'total_tables_identified': len(set().union(*[
                    [t['full_name'] for t in proc['tables']] 
                    for proc in analysis_data
                ])),
                'total_joins_identified': sum(len(proc['joins']) for proc in analysis_data),
                'total_conditions_identified': sum(len(proc['where_conditions']) for proc in analysis_data)
            },
            'next_steps': [
                "Review the generated documentation files",
                "Validate table and column mappings",
                "Enhance business logic descriptions",
                "Use the LLM-optimized summary for requirements documentation"
            ],
            'output_files': [
                "raw_analysis_data.json",
                "table_catalog.json", 
                "data_lineage.json",
                "business_logic.json",
                "llm_optimized_summary.json",
                "consolidated_requirements_report.json"
            ]
        }
        
        summary_file = self.output_dir / "workflow_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Workflow summary saved to: {summary_file}")
        print(f"\n📁 All output files are in: {self.output_dir}")

def main():
    """Main function"""
    workflow = CompleteAnalysisWorkflow()
    
    try:
        success = workflow.run_complete_workflow()
        if success:
            print("\n🎯 Analysis complete! Check the analysis_output directory for results.")
        else:
            print("\n❌ Analysis failed. Please check the errors above.")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ Analysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
