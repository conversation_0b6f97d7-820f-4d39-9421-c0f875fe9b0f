#!/usr/bin/env python3
"""
LLM-Powered Requirements Generator v2 (Working Version)
=======================================================

This script uses Azure OpenAI via direct HTTP requests to generate comprehensive 
requirements documentation from ANTLR-parsed SQL procedure metadata.

This version uses the working requests approach instead of the OpenAI client library.
"""

import json
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

class LLMRequirementsGeneratorV2:
    """Generate requirements documentation using Azure OpenAI via HTTP requests"""
    
    def __init__(self, analysis_output_dir: str = "analysis_output"):
        self.analysis_output_dir = Path(analysis_output_dir)
        self.output_dir = self.analysis_output_dir / "llm_generated"
        self.output_dir.mkdir(exist_ok=True)
        
        # Azure OpenAI configuration - Your exact API setup
        self.endpoint = "https://models.inference.ai.azure.com"
        self.model = "gpt-4o"
        self.token = "****************************************"
        
        # HTTP headers for API calls
        self.headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        
        # Load analysis data
        self.analysis_data = self._load_analysis_data()
        print("✅ LLM Requirements Generator v2 initialized")
    
    def _load_analysis_data(self) -> Dict[str, Any]:
        """Load all analysis data from JSON files"""
        data = {}
        
        files_to_load = [
            'consolidated_requirements_report.json',
            'table_catalog.json',
            'business_logic.json',
            'data_lineage.json',
            'raw_analysis_data.json'
        ]
        
        for filename in files_to_load:
            file_path = self.analysis_output_dir / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data[filename.replace('.json', '')] = json.load(f)
                    print(f"✅ Loaded {filename}")
                except Exception as e:
                    print(f"⚠️ Warning: Could not load {filename}: {e}")
            else:
                print(f"⚠️ Warning: {filename} not found")
        
        return data
    
    def _call_llm(self, system_message: str, user_message: str, temperature: float = 0.7, max_tokens: int = 3000) -> str:
        """Call Azure OpenAI API using HTTP requests"""
        try:
            data = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                "max_tokens": max_tokens,
                "temperature": temperature
            }
            
            response = requests.post(
                f"{self.endpoint}/chat/completions",
                headers=self.headers,
                json=data,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                error_msg = f"API call failed with status {response.status_code}: {response.text}"
                print(f"❌ {error_msg}")
                return f"Error: {error_msg}"
                
        except Exception as e:
            error_msg = f"Error calling LLM: {e}"
            print(f"❌ {error_msg}")
            return f"Error: {error_msg}"
    
    def test_connection(self) -> bool:
        """Test LLM API connection"""
        print("🔍 Testing LLM connection...")
        
        response = self._call_llm(
            "You are a helpful assistant.",
            "Say 'SAP HANA analysis system ready!' if you can respond.",
            max_tokens=50
        )
        
        if response and not response.startswith("Error"):
            print("✅ LLM connection successful!")
            print(f"Response: {response}")
            return True
        else:
            print(f"❌ LLM connection failed: {response}")
            return False
    
    def generate_business_requirements(self) -> str:
        """Generate comprehensive business requirements"""
        print("📋 Generating business requirements...")
        
        system_message = """You are a senior SAP business analyst with 15+ years of experience in ERP implementations. 
        
        Generate comprehensive business requirements documentation based on SAP HANA SQL procedure analysis.
        
        Focus on:
        - Business processes and workflows
        - Functional requirements
        - Data integration needs
        - Stakeholder value proposition
        
        Write in professional, business-friendly language suitable for executives and project managers."""
        
        table_catalog = self.analysis_data.get('table_catalog', {})
        raw_data = self.analysis_data.get('raw_analysis_data', [])
        
        user_message = f"""
        Based on this SAP HANA SQL analysis, generate comprehensive business requirements:

        ## Analysis Summary
        - Total procedures analyzed: {len(raw_data)}
        - Tables identified: {table_catalog.get('summary', {}).get('total_tables', 0)}
        - Most used tables: {table_catalog.get('summary', {}).get('most_used_tables', [])}

        ## Key Tables and Their Business Purpose
        {json.dumps(table_catalog.get('tables', {}), indent=2)}

        ## Sample Procedures Analyzed
        {json.dumps([proc.get('procedure_name', '') for proc in raw_data[:5]], indent=2)}

        Generate a professional business requirements document with:
        1. Executive Summary
        2. Business Processes Supported
        3. Functional Requirements
        4. Data Integration Requirements
        5. Success Criteria
        6. Implementation Recommendations
        """
        
        return self._call_llm(system_message, user_message, max_tokens=4000)
    
    def generate_technical_specifications(self) -> str:
        """Generate technical specifications"""
        print("⚙️ Generating technical specifications...")
        
        system_message = """You are a senior SAP HANA technical architect with expertise in database optimization and system integration.
        
        Generate detailed technical specifications based on SQL procedure analysis.
        
        Focus on:
        - Database architecture and performance
        - Integration patterns and APIs
        - Technical constraints and requirements
        - Implementation guidelines
        
        Use technical language appropriate for developers and system architects."""
        
        data_lineage = self.analysis_data.get('data_lineage', {})
        raw_data = self.analysis_data.get('raw_analysis_data', [])
        
        user_message = f"""
        Generate technical specifications based on this SAP HANA analysis:

        ## Data Lineage and Relationships
        {json.dumps(data_lineage, indent=2)}

        ## Procedure Analysis Details
        {json.dumps(raw_data[:3], indent=2)}

        Create technical specifications covering:
        1. Database Schema and Architecture
        2. Performance Optimization Requirements
        3. Integration Architecture
        4. Security and Access Control
        5. Monitoring and Maintenance
        6. Development Guidelines
        """
        
        return self._call_llm(system_message, user_message, max_tokens=4000)
    
    def generate_data_dictionary(self) -> str:
        """Generate comprehensive data dictionary"""
        print("📚 Generating data dictionary...")
        
        system_message = """You are a data governance expert specializing in SAP systems.
        
        Create a comprehensive data dictionary that explains tables and their business context.
        
        Focus on:
        - Business meaning of each table
        - Data relationships and dependencies
        - Usage patterns and frequency
        - Data quality considerations
        
        Make it accessible to both technical and business users."""
        
        table_catalog = self.analysis_data.get('table_catalog', {})
        
        user_message = f"""
        Create a comprehensive data dictionary based on this table analysis:

        {json.dumps(table_catalog, indent=2)}

        For each table, provide:
        1. Business Purpose and Context
        2. Key Fields and Their Meaning
        3. Relationships to Other Tables
        4. Usage Patterns and Frequency
        5. Data Quality Requirements
        6. Business Rules and Constraints
        """
        
        return self._call_llm(system_message, user_message, max_tokens=4000)
    
    def generate_migration_strategy(self) -> str:
        """Generate migration and modernization strategy"""
        print("🚀 Generating migration strategy...")
        
        system_message = """You are a SAP migration specialist with expertise in system modernization and digital transformation.
        
        Provide strategic guidance for system migration and modernization.
        
        Focus on:
        - Migration approach and phases
        - Risk assessment and mitigation
        - Modernization opportunities
        - Implementation roadmap
        
        Provide actionable recommendations with timelines and success metrics."""
        
        consolidated_report = self.analysis_data.get('consolidated_requirements_report', {})
        
        user_message = f"""
        Based on this comprehensive SAP system analysis, create a migration and modernization strategy:

        {json.dumps(consolidated_report, indent=2)}

        Provide:
        1. Current State Assessment
        2. Target State Vision
        3. Migration Strategy and Phases
        4. Risk Assessment and Mitigation
        5. Modernization Opportunities
        6. Implementation Roadmap with Timelines
        7. Success Metrics and KPIs
        """
        
        return self._call_llm(system_message, user_message, max_tokens=4000)
    
    def generate_all_documentation(self):
        """Generate all types of documentation"""
        print("🤖 Starting LLM-powered requirements generation...")
        print("=" * 60)
        
        if not self.analysis_data:
            print("❌ No analysis data found. Please run the SQL analysis first.")
            return
        
        # Test connection first
        if not self.test_connection():
            print("❌ LLM connection failed. Cannot generate documentation.")
            return
        
        # Generate different types of documentation
        documents = {
            'business_requirements.md': self.generate_business_requirements(),
            'technical_specifications.md': self.generate_technical_specifications(),
            'data_dictionary.md': self.generate_data_dictionary(),
            'migration_strategy.md': self.generate_migration_strategy()
        }
        
        # Save all documents
        successful_docs = []
        for filename, content in documents.items():
            if content and not content.startswith("Error"):
                file_path = self.output_dir / filename
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ Generated: {filename}")
                successful_docs.append(filename)
            else:
                print(f"❌ Failed to generate: {filename}")
        
        # Generate summary report
        self._generate_summary_report(successful_docs)
        
        print(f"\n🎉 All documentation generated in: {self.output_dir}")
        return successful_docs
    
    def _generate_summary_report(self, successful_docs: List[str]):
        """Generate a summary report"""
        summary = f"""# SAP HANA SQL Requirements Documentation Summary

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Generated by: LLM Requirements Generator v2

## Analysis Overview
- **Total Procedures Analyzed**: {len(self.analysis_data.get('raw_analysis_data', []))}
- **Tables Identified**: {self.analysis_data.get('table_catalog', {}).get('summary', {}).get('total_tables', 0)}
- **Documentation Generated**: {len(successful_docs)} files

## Generated Documents
{chr(10).join([f"- {doc}" for doc in successful_docs])}

## Key Findings
- **Core Business Processes**: Customer Order Management, Material Management, Vendor Payment Processing
- **Critical Tables**: VBAK, VBAP, KNA1, MARA, MARD, MAKT, BKPF, BSEG, LFA1
- **Integration Points**: SAP ECC/S4HANA to SAP HANA data flows

## Next Steps
1. Review generated documentation with business stakeholders
2. Validate technical specifications with IT architecture team
3. Use migration strategy for project planning
4. Implement data governance based on data dictionary

## Files Location
All generated files are available in: `{self.output_dir}`
"""
        
        summary_path = self.output_dir / "README.md"
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary)

def main():
    """Main function"""
    print("🤖 LLM-Powered SAP HANA Requirements Generator v2")
    print("=" * 55)
    
    # Check if analysis data exists
    analysis_dir = Path("analysis_output")
    if not analysis_dir.exists():
        print("❌ Analysis output directory not found.")
        print("Please run 'python run_complete_analysis.py' first.")
        return
    
    # Initialize and run generator
    generator = LLMRequirementsGeneratorV2()
    successful_docs = generator.generate_all_documentation()
    
    if successful_docs:
        print(f"\n✅ Successfully generated {len(successful_docs)} documents!")
        print("🎯 Ready for stakeholder review and implementation planning.")
    else:
        print("\n❌ No documents were successfully generated.")

if __name__ == "__main__":
    main()
