Procedure ID,Table Name,Column Name,Used As,Filter Condition,Join <PERSON>dition,Notes
PR_GS_O2C_ADDRC_DELTA,CDHDR,"CDHDR.MANDANT, CDHDR.OBJECTCLAS, CDHDR.OBJECTID, CDHDR.CHANGENR, CDHDR.UDATE, CDHDR.UTIME, CDHDR.USERNAME, CDHDR.TCODE","SELECT, JOIN",CDHDR.OBJECTCLAS = 'BELEG',CDPOS.MANDANT = CDHDR.MANDANT AND CDPOS.OBJECTCLAS = CDHDR.OBJECTCLAS AND CDPOS.OBJECTID = CDHDR.OBJECTID AND CDPOS.CHANGENR = CDHDR.CHANGENR,Change document header - tracks all changes made to business documents
PR_GS_O2C_ADDRC_DELTA,CDPOS,"CDPOS.MANDANT, CDPOS.OBJECTCLAS, CDPOS.OBJECTID, CDPOS.CHANGENR, CDPOS.FNAME, CDPOS.TABNAME, CDPOS.VALUE_OLD, CDPOS.VALUE_NEW, CDPOS.TABKEY","SELECT, JOIN",CDPOS.FNAME = 'RSTGR' AND CDPOS.TABNAME = 'BSEG',CDPOS.TABKEY = BSEG_INV.MANDT || BSEG_INV.BUKRS || BSEG_INV.BELNR || BSEG_INV.GJAHR || BSEG_INV.BUZEI,Change document items - detailed field-level changes for dispute reason codes
PR_GS_O2C_ADDRC_DELTA,BSEG,"BSEG_INV.MANDT, BSEG_INV.BUKRS, BSEG_INV.BELNR, BSEG_INV.GJAHR, BSEG_INV.BUZEI, BSEG_INV.BSCHL, BSEG_INV.KOART",JOIN,"BSEG_INV.BSCHL IN ('01','11') AND BSEG_INV.KOART = 'D'",BSEG_INV.MANDT = CDPOS.MANDANT AND BSEG_INV.MANDT = BKPF_INV.MANDT AND BSEG_INV.BUKRS = BKPF_INV.BUKRS AND BSEG_INV.BELNR = BKPF_INV.BELNR AND BSEG_INV.GJAHR = BKPF_INV.GJAHR,Accounting document line items - customer line items for invoice documents
PR_GS_O2C_ADDRC_DELTA,BKPF,"BKPF_INV.MANDT, BKPF_INV.BUKRS, BKPF_INV.BELNR, BKPF_INV.GJAHR, BKPF_INV.AWTYP, BKPF_INV.AWKEY","SELECT, JOIN",BKPF_INV.AWTYP = 'VBRK',VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY,Accounting document header - links accounting docs to billing documents
PR_GS_O2C_ADDRC_DELTA,VBRK,"VBRK.MANDT, VBRK.VBELN",JOIN,,VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN,Billing document header - invoice/credit memo headers
PR_GS_O2C_ADDRC_DELTA,VBRP,"VBRP.MANDT, VBRP.VBELN, VBRP.AUBEL, VBRP.AUPOS",JOIN,,VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR,Billing document items - links billing items back to sales orders
PR_GS_O2C_ADDRC_DELTA,VBAP,"VBAP.MANDT, VBAP.VBELN, VBAP.POSNR","SELECT, JOIN",,VBAK.VBELN = VBAP.VBELN,Sales document items - individual line items in sales orders
PR_GS_O2C_ADDRC_DELTA,VBAK,"VBAK.VBELN, VBAK.VBTYP",JOIN,VBAK.VBTYP = 'C',VBAK.VBELN = VBAP.VBELN,Sales document header - sales order headers (type C = standard orders)
PR_GS_O2C_ADDRC_DELTA,USR02,"USR02.MANDT, USR02.BNAME, USR02.USTYP","SELECT, LEFT JOIN",,USR02.MANDT = CDHDR.MANDANT AND USR02.BNAME = CDHDR.USERNAME,User master record - current user information for change tracking
PR_GS_O2C_ADDRC_DELTA,USH02,"USH02.MANDT, USH02.BNAME, USH02.USTYP","SELECT, LEFT JOIN",,USH02.MANDT = CDHDR.MANDANT AND USH02.BNAME = CDHDR.USERNAME,User master record (historical) - historical user data for audit trail
PR_GS_O2C_CUSTOMER_DELTA,CDHDR,"CDHDR.MANDANT, CDHDR.OBJECTCLAS, CDHDR.OBJECTID, CDHDR.CHANGENR, CDHDR.UDATE, CDHDR.UTIME, CDHDR.USERNAME, CDHDR.TCODE","SELECT, JOIN",CDHDR.OBJECTCLAS = 'BELEG',CDPOS.MANDANT = CDHDR.MANDANT AND CDPOS.OBJECTCLAS = CDHDR.OBJECTCLAS AND CDPOS.OBJECTID = CDHDR.OBJECTID AND CDPOS.CHANGENR = CDHDR.CHANGENR,Change document header - tracks all changes made to business documents
PR_GS_O2C_CUSTOMER_DELTA,CDPOS,"CDPOS.MANDANT, CDPOS.OBJECTCLAS, CDPOS.OBJECTID, CDPOS.CHANGENR, CDPOS.FNAME, CDPOS.TABNAME, CDPOS.VALUE_OLD, CDPOS.VALUE_NEW, CDPOS.TABKEY","SELECT, JOIN",CDPOS.FNAME = 'RSTGR' AND CDPOS.TABNAME = 'BSEG',CDPOS.TABKEY = BSEG_INV.MANDT || BSEG_INV.BUKRS || BSEG_INV.BELNR || BSEG_INV.GJAHR || BSEG_INV.BUZEI,Change document items - detailed field-level changes for dispute reason codes
PR_GS_O2C_CUSTOMER_DELTA,BSEG,"BSEG_INV.MANDT, BSEG_INV.BUKRS, BSEG_INV.BELNR, BSEG_INV.GJAHR, BSEG_INV.BUZEI, BSEG_INV.BSCHL, BSEG_INV.KOART",JOIN,"BSEG_INV.BSCHL IN ('01','11') AND BSEG_INV.KOART = 'D'",BSEG_INV.MANDT = CDPOS.MANDANT AND BSEG_INV.MANDT = BKPF_INV.MANDT AND BSEG_INV.BUKRS = BKPF_INV.BUKRS AND BSEG_INV.BELNR = BKPF_INV.BELNR AND BSEG_INV.GJAHR = BKPF_INV.GJAHR,Accounting document line items - customer line items for invoice documents
PR_GS_O2C_CUSTOMER_DELTA,BKPF,"BKPF_INV.MANDT, BKPF_INV.BUKRS, BKPF_INV.BELNR, BKPF_INV.GJAHR, BKPF_INV.AWTYP, BKPF_INV.AWKEY","SELECT, JOIN",BKPF_INV.AWTYP = 'VBRK',VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY,Accounting document header - links accounting docs to billing documents
PR_GS_O2C_CUSTOMER_DELTA,VBRK,"VBRK.MANDT, VBRK.VBELN",JOIN,,VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN,Billing document header - invoice/credit memo headers
PR_GS_O2C_CUSTOMER_DELTA,VBRP,"VBRP.MANDT, VBRP.VBELN, VBRP.AUBEL, VBRP.AUPOS",JOIN,,VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR,Billing document items - links billing items back to sales orders
PR_GS_O2C_CUSTOMER_DELTA,VBAP,"VBAP.MANDT, VBAP.VBELN, VBAP.POSNR","SELECT, JOIN",,VBAK.VBELN = VBAP.VBELN,Sales document items - individual line items in sales orders
PR_GS_O2C_CUSTOMER_DELTA,VBAK,"VBAK.VBELN, VBAK.VBTYP",JOIN,VBAK.VBTYP = 'C',VBAK.VBELN = VBAP.VBELN,Sales document header - sales order headers (type C = standard orders)
PR_GS_O2C_CUSTOMER_DELTA,USR02,"USR02.MANDT, USR02.BNAME, USR02.USTYP","SELECT, LEFT JOIN",,USR02.MANDT = CDHDR.MANDANT AND USR02.BNAME = CDHDR.USERNAME,User master record - current user information for change tracking
PR_GS_O2C_CUSTOMER_DELTA,USH02,"USH02.MANDT, USH02.BNAME, USH02.USTYP","SELECT, LEFT JOIN",,USH02.MANDT = CDHDR.MANDANT AND USH02.BNAME = CDHDR.USERNAME,User master record (historical) - historical user data for audit trail
PR_GS_O2C_MATERIAL_DELTA,CDHDR,"CDHDR.MANDANT, CDHDR.OBJECTCLAS, CDHDR.OBJECTID, CDHDR.CHANGENR, CDHDR.UDATE, CDHDR.UTIME, CDHDR.USERNAME, CDHDR.TCODE","SELECT, JOIN",CDHDR.OBJECTCLAS = 'BELEG',CDPOS.MANDANT = CDHDR.MANDANT AND CDPOS.OBJECTCLAS = CDHDR.OBJECTCLAS AND CDPOS.OBJECTID = CDHDR.OBJECTID AND CDPOS.CHANGENR = CDHDR.CHANGENR,Change document header - tracks all changes made to business documents
PR_GS_O2C_MATERIAL_DELTA,CDPOS,"CDPOS.MANDANT, CDPOS.OBJECTCLAS, CDPOS.OBJECTID, CDPOS.CHANGENR, CDPOS.FNAME, CDPOS.TABNAME, CDPOS.VALUE_OLD, CDPOS.VALUE_NEW, CDPOS.TABKEY","SELECT, JOIN",CDPOS.FNAME = 'RSTGR' AND CDPOS.TABNAME = 'BSEG',CDPOS.TABKEY = BSEG_INV.MANDT || BSEG_INV.BUKRS || BSEG_INV.BELNR || BSEG_INV.GJAHR || BSEG_INV.BUZEI,Change document items - detailed field-level changes for dispute reason codes
PR_GS_O2C_MATERIAL_DELTA,BSEG,"BSEG_INV.MANDT, BSEG_INV.BUKRS, BSEG_INV.BELNR, BSEG_INV.GJAHR, BSEG_INV.BUZEI, BSEG_INV.BSCHL, BSEG_INV.KOART",JOIN,"BSEG_INV.BSCHL IN ('01','11') AND BSEG_INV.KOART = 'D'",BSEG_INV.MANDT = CDPOS.MANDANT AND BSEG_INV.MANDT = BKPF_INV.MANDT AND BSEG_INV.BUKRS = BKPF_INV.BUKRS AND BSEG_INV.BELNR = BKPF_INV.BELNR AND BSEG_INV.GJAHR = BKPF_INV.GJAHR,Accounting document line items - customer line items for invoice documents
PR_GS_O2C_MATERIAL_DELTA,BKPF,"BKPF_INV.MANDT, BKPF_INV.BUKRS, BKPF_INV.BELNR, BKPF_INV.GJAHR, BKPF_INV.AWTYP, BKPF_INV.AWKEY","SELECT, JOIN",BKPF_INV.AWTYP = 'VBRK',VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY,Accounting document header - links accounting docs to billing documents
PR_GS_O2C_MATERIAL_DELTA,VBRK,"VBRK.MANDT, VBRK.VBELN",JOIN,,VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN,Billing document header - invoice/credit memo headers
PR_GS_O2C_MATERIAL_DELTA,VBRP,"VBRP.MANDT, VBRP.VBELN, VBRP.AUBEL, VBRP.AUPOS",JOIN,,VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR,Billing document items - links billing items back to sales orders
PR_GS_O2C_MATERIAL_DELTA,VBAP,"VBAP.MANDT, VBAP.VBELN, VBAP.POSNR","SELECT, JOIN",,VBAK.VBELN = VBAP.VBELN,Sales document items - individual line items in sales orders
PR_GS_O2C_MATERIAL_DELTA,VBAK,"VBAK.VBELN, VBAK.VBTYP",JOIN,VBAK.VBTYP = 'C',VBAK.VBELN = VBAP.VBELN,Sales document header - sales order headers (type C = standard orders)
PR_GS_O2C_MATERIAL_DELTA,USR02,"USR02.MANDT, USR02.BNAME, USR02.USTYP","SELECT, LEFT JOIN",,USR02.MANDT = CDHDR.MANDANT AND USR02.BNAME = CDHDR.USERNAME,User master record - current user information for change tracking
PR_GS_O2C_MATERIAL_DELTA,USH02,"USH02.MANDT, USH02.BNAME, USH02.USTYP","SELECT, LEFT JOIN",,USH02.MANDT = CDHDR.MANDANT AND USH02.BNAME = CDHDR.USERNAME,User master record (historical) - historical user data for audit trail
PR_GS_O2C_VENDOR_DELTA,CDHDR,"CDHDR.MANDANT, CDHDR.OBJECTCLAS, CDHDR.OBJECTID, CDHDR.CHANGENR, CDHDR.UDATE, CDHDR.UTIME, CDHDR.USERNAME, CDHDR.TCODE","SELECT, JOIN",CDHDR.OBJECTCLAS = 'BELEG',CDPOS.MANDANT = CDHDR.MANDANT AND CDPOS.OBJECTCLAS = CDHDR.OBJECTCLAS AND CDPOS.OBJECTID = CDHDR.OBJECTID AND CDPOS.CHANGENR = CDHDR.CHANGENR,Change document header - tracks all changes made to business documents
PR_GS_O2C_VENDOR_DELTA,CDPOS,"CDPOS.MANDANT, CDPOS.OBJECTCLAS, CDPOS.OBJECTID, CDPOS.CHANGENR, CDPOS.FNAME, CDPOS.TABNAME, CDPOS.VALUE_OLD, CDPOS.VALUE_NEW, CDPOS.TABKEY","SELECT, JOIN",CDPOS.FNAME = 'RSTGR' AND CDPOS.TABNAME = 'BSEG',CDPOS.TABKEY = BSEG_INV.MANDT || BSEG_INV.BUKRS || BSEG_INV.BELNR || BSEG_INV.GJAHR || BSEG_INV.BUZEI,Change document items - detailed field-level changes for dispute reason codes
PR_GS_O2C_VENDOR_DELTA,BSEG,"BSEG_INV.MANDT, BSEG_INV.BUKRS, BSEG_INV.BELNR, BSEG_INV.GJAHR, BSEG_INV.BUZEI, BSEG_INV.BSCHL, BSEG_INV.KOART",JOIN,"BSEG_INV.BSCHL IN ('01','11') AND BSEG_INV.KOART = 'D'",BSEG_INV.MANDT = CDPOS.MANDANT AND BSEG_INV.MANDT = BKPF_INV.MANDT AND BSEG_INV.BUKRS = BKPF_INV.BUKRS AND BSEG_INV.BELNR = BKPF_INV.BELNR AND BSEG_INV.GJAHR = BKPF_INV.GJAHR,Accounting document line items - customer line items for invoice documents
PR_GS_O2C_VENDOR_DELTA,BKPF,"BKPF_INV.MANDT, BKPF_INV.BUKRS, BKPF_INV.BELNR, BKPF_INV.GJAHR, BKPF_INV.AWTYP, BKPF_INV.AWKEY","SELECT, JOIN",BKPF_INV.AWTYP = 'VBRK',VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY,Accounting document header - links accounting docs to billing documents
PR_GS_O2C_VENDOR_DELTA,VBRK,"VBRK.MANDT, VBRK.VBELN",JOIN,,VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN,Billing document header - invoice/credit memo headers
PR_GS_O2C_VENDOR_DELTA,VBRP,"VBRP.MANDT, VBRP.VBELN, VBRP.AUBEL, VBRP.AUPOS",JOIN,,VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR,Billing document items - links billing items back to sales orders
PR_GS_O2C_VENDOR_DELTA,VBAP,"VBAP.MANDT, VBAP.VBELN, VBAP.POSNR","SELECT, JOIN",,VBAK.VBELN = VBAP.VBELN,Sales document items - individual line items in sales orders
PR_GS_O2C_VENDOR_DELTA,VBAK,"VBAK.VBELN, VBAK.VBTYP",JOIN,VBAK.VBTYP = 'C',VBAK.VBELN = VBAP.VBELN,Sales document header - sales order headers (type C = standard orders)
PR_GS_O2C_VENDOR_DELTA,USR02,"USR02.MANDT, USR02.BNAME, USR02.USTYP","SELECT, LEFT JOIN",,USR02.MANDT = CDHDR.MANDANT AND USR02.BNAME = CDHDR.USERNAME,User master record - current user information for change tracking
PR_GS_O2C_VENDOR_DELTA,USH02,"USH02.MANDT, USH02.BNAME, USH02.USTYP","SELECT, LEFT JOIN",,USH02.MANDT = CDHDR.MANDANT AND USH02.BNAME = CDHDR.USERNAME,User master record (historical) - historical user data for audit trail
PR_GS_O2C_INVOICE_DELTA,CDHDR,"CDHDR.MANDANT, CDHDR.OBJECTCLAS, CDHDR.OBJECTID, CDHDR.CHANGENR, CDHDR.UDATE, CDHDR.UTIME, CDHDR.USERNAME, CDHDR.TCODE","SELECT, JOIN",CDHDR.OBJECTCLAS = 'BELEG',CDPOS.MANDANT = CDHDR.MANDANT AND CDPOS.OBJECTCLAS = CDHDR.OBJECTCLAS AND CDPOS.OBJECTID = CDHDR.OBJECTID AND CDPOS.CHANGENR = CDHDR.CHANGENR,Change document header - tracks all changes made to business documents
PR_GS_O2C_INVOICE_DELTA,CDPOS,"CDPOS.MANDANT, CDPOS.OBJECTCLAS, CDPOS.OBJECTID, CDPOS.CHANGENR, CDPOS.FNAME, CDPOS.TABNAME, CDPOS.VALUE_OLD, CDPOS.VALUE_NEW, CDPOS.TABKEY","SELECT, JOIN",CDPOS.FNAME = 'RSTGR' AND CDPOS.TABNAME = 'BSEG',CDPOS.TABKEY = BSEG_INV.MANDT || BSEG_INV.BUKRS || BSEG_INV.BELNR || BSEG_INV.GJAHR || BSEG_INV.BUZEI,Change document items - detailed field-level changes for dispute reason codes
PR_GS_O2C_INVOICE_DELTA,BSEG,"BSEG_INV.MANDT, BSEG_INV.BUKRS, BSEG_INV.BELNR, BSEG_INV.GJAHR, BSEG_INV.BUZEI, BSEG_INV.BSCHL, BSEG_INV.KOART",JOIN,"BSEG_INV.BSCHL IN ('01','11') AND BSEG_INV.KOART = 'D'",BSEG_INV.MANDT = CDPOS.MANDANT AND BSEG_INV.MANDT = BKPF_INV.MANDT AND BSEG_INV.BUKRS = BKPF_INV.BUKRS AND BSEG_INV.BELNR = BKPF_INV.BELNR AND BSEG_INV.GJAHR = BKPF_INV.GJAHR,Accounting document line items - customer line items for invoice documents
PR_GS_O2C_INVOICE_DELTA,BKPF,"BKPF_INV.MANDT, BKPF_INV.BUKRS, BKPF_INV.BELNR, BKPF_INV.GJAHR, BKPF_INV.AWTYP, BKPF_INV.AWKEY","SELECT, JOIN",BKPF_INV.AWTYP = 'VBRK',VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY,Accounting document header - links accounting docs to billing documents
PR_GS_O2C_INVOICE_DELTA,VBRK,"VBRK.MANDT, VBRK.VBELN",JOIN,,VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN,Billing document header - invoice/credit memo headers
PR_GS_O2C_INVOICE_DELTA,VBRP,"VBRP.MANDT, VBRP.VBELN, VBRP.AUBEL, VBRP.AUPOS",JOIN,,VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR,Billing document items - links billing items back to sales orders
PR_GS_O2C_INVOICE_DELTA,VBAP,"VBAP.MANDT, VBAP.VBELN, VBAP.POSNR","SELECT, JOIN",,VBAK.VBELN = VBAP.VBELN,Sales document items - individual line items in sales orders
PR_GS_O2C_INVOICE_DELTA,VBAK,"VBAK.VBELN, VBAK.VBTYP",JOIN,VBAK.VBTYP = 'C',VBAK.VBELN = VBAP.VBELN,Sales document header - sales order headers (type C = standard orders)
PR_GS_O2C_INVOICE_DELTA,USR02,"USR02.MANDT, USR02.BNAME, USR02.USTYP","SELECT, LEFT JOIN",,USR02.MANDT = CDHDR.MANDANT AND USR02.BNAME = CDHDR.USERNAME,User master record - current user information for change tracking
PR_GS_O2C_INVOICE_DELTA,USH02,"USH02.MANDT, USH02.BNAME, USH02.USTYP","SELECT, LEFT JOIN",,USH02.MANDT = CDHDR.MANDANT AND USH02.BNAME = CDHDR.USERNAME,User master record (historical) - historical user data for audit trail
