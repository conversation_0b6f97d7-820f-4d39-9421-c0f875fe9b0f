#!/usr/bin/env python3
"""
Simple BSEG Test
================

Simple test to check BSEG column extraction.
"""

from sql_parser import AdvancedSQLParser

# Real SQL with lowercase table names and aliases
sql = '''
SELECT 
    h.bukrs as company_code,
    h.belnr as document_number,
    h.bldat as document_date,
    i.lifnr as vendor_number,
    i.dmbtr as amount_local_currency,
    i.koart as account_type
FROM bkpf h
INNER JOIN bseg i ON h.belnr = i.belnr 
WHERE h.bukrs = 'US01'
  AND i.koart = 'K'
  AND i.lifnr IS NOT NULL;
'''

parser = AdvancedSQLParser()

print("🔍 Testing BSEG parsing...")
print(f"SQL: {sql}")

# Test table extraction
tables = parser._extract_tables_enhanced(sql)
print(f"\n📊 Tables found: {tables}")

# Test alias mapping
aliases = parser._build_alias_mapping(sql)
print(f"🔧 Alias mapping: {aliases}")

# Test column extraction for BSEG
bseg_columns = parser._extract_columns_for_table(sql, 'BSEG')
print(f"📋 BSEG columns: {sorted(list(bseg_columns))}")

# Test column extraction for BKPF
bkpf_columns = parser._extract_columns_for_table(sql, 'BKPF')
print(f"📋 BKPF columns: {sorted(list(bkpf_columns))}")

# Test full parsing
result = parser.parse_procedure(sql, "test_bseg")
print(f"\n📊 Full parsing results:")
print(f"   Tables: {list(result.tables.keys())}")
for table_name, table_ref in result.tables.items():
    print(f"   {table_name} columns: {sorted(list(table_ref.columns))}")

print(f"   Filters: {len(result.filters)}")
for filter_cond in result.filters:
    print(f"     {filter_cond.table}.{filter_cond.column} {filter_cond.operator} {filter_cond.values}")
