"""
Advanced SAP HANA SQL Parser for Migration Analysis
==================================================

This module provides sophisticated SQL parsing capabilities for SAP HANA procedures,
focusing on migration requirements analysis including:
- Complex join relationship analysis
- Filter condition consolidation across procedures
- Data lineage and dependency mapping
"""

import re
import sqlparse
import sys
import os
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict, Counter
import networkx as nx

# Try to import ANTLR if available
try:
    # Add the parent directory to path to access ANTLR generated files
    parent_dir = Path(__file__).parent.parent
    generated_dir = parent_dir / "generated"
    if generated_dir.exists():
        sys.path.insert(0, str(generated_dir))

        from antlr4 import *
        from HanaLexer import HanaLexer
        from HanaParser import HanaParser
        from HanaListener import HanaListener
        ANTLR_AVAILABLE = True
        print("✅ ANTLR SAP HANA grammar available")
    else:
        ANTLR_AVAILABLE = False
        print("⚠️ ANTLR generated files not found, using sqlparse fallback")
except ImportError:
    ANTLR_AVAILABLE = False
    print("⚠️ ANTLR not available, using sqlparse fallback")

@dataclass
class TableReference:
    """Represents a table reference with all its usage details"""
    name: str
    schema: Optional[str] = None
    alias: Optional[str] = None
    columns: Set[str] = field(default_factory=set)
    usage_contexts: Set[str] = field(default_factory=set)  # SELECT, JOIN, WHERE, etc.
    
    @property
    def full_name(self) -> str:
        return f"{self.schema}.{self.name}" if self.schema else self.name

@dataclass
class FilterCondition:
    """Represents a filter condition with detailed analysis"""
    table: str
    column: str
    operator: str
    values: List[str]
    condition_text: str
    procedure_id: str
    is_parameter: bool = False
    
@dataclass
class JoinRelationship:
    """Represents a join relationship between tables"""
    left_table: str
    right_table: str
    join_type: str
    condition: str
    procedure_id: str
    left_columns: List[str] = field(default_factory=list)
    right_columns: List[str] = field(default_factory=list)

@dataclass
class ProcedureAnalysis:
    """Complete analysis of a single procedure"""
    procedure_id: str
    tables: Dict[str, TableReference]
    filters: List[FilterCondition]
    joins: List[JoinRelationship]
    parameters: List[Dict[str, Any]]
    raw_sql: str
    complexity_score: int = 0

class SAP_HANA_Listener(HanaListener if ANTLR_AVAILABLE else object):
    """ANTLR listener for SAP HANA SQL parsing"""

    def __init__(self):
        self.tables = set()
        self.columns = defaultdict(set)
        self.joins = []
        self.filters = []
        self.current_table_alias = {}

    def enterTableName(self, ctx):
        """Extract table names"""
        if hasattr(ctx, 'getText'):
            table_name = ctx.getText().strip('"').upper()
            if len(table_name) >= 3 and table_name.isalpha():
                self.tables.add(table_name)

    def enterColumnName(self, ctx):
        """Extract column references"""
        if hasattr(ctx, 'getText'):
            column_ref = ctx.getText()
            if '.' in column_ref:
                table, column = column_ref.split('.', 1)
                self.columns[table.upper()].add(column.upper())

class AdvancedSQLParser:
    """Advanced SQL parser for SAP HANA migration analysis with ANTLR support"""

    def __init__(self):
        self.use_antlr = ANTLR_AVAILABLE
        self.sap_table_patterns = {
            'VBAK': 'Sales Document Header',
            'VBAP': 'Sales Document Items',
            'VBRK': 'Billing Document Header',
            'VBRP': 'Billing Document Items',
            'BKPF': 'Accounting Document Header',
            'BSEG': 'Accounting Document Line Items',
            'CDHDR': 'Change Document Header',
            'CDPOS': 'Change Document Items',
            'KNA1': 'Customer Master',
            'LFA1': 'Vendor Master',
            'MARA': 'Material Master',
            'MARD': 'Material Stock Data',
            'MAKT': 'Material Descriptions',
            'USR02': 'User Master Record',
            'USH02': 'User Master Record (Historical)'
        }

        print(f"🔧 SQL Parser initialized with {'ANTLR' if self.use_antlr else 'sqlparse'} backend")

    def analyze_cross_procedure_patterns(self, procedures: List[ProcedureAnalysis]) -> Dict[str, Any]:
        """Analyze patterns across multiple procedures for migration insights"""

        # Consolidate tables across procedures (one row per table)
        table_consolidation = self._consolidate_tables_across_procedures(procedures)

        # Consolidate filters across procedures
        filter_analysis = self._analyze_filter_patterns(procedures)

        # Analyze join patterns
        join_analysis = self._analyze_join_patterns(procedures)

        # Create data lineage graph
        lineage_graph = self._create_data_lineage(procedures)

        # Identify migration critical points
        critical_points = self._identify_critical_migration_points(procedures)

        return {
            'table_consolidation': table_consolidation,
            'filter_analysis': filter_analysis,
            'join_analysis': join_analysis,
            'data_lineage': lineage_graph,
            'critical_points': critical_points,
            'summary': {
                'total_procedures': len(procedures),
                'unique_tables': len(set().union(*[p.tables.keys() for p in procedures])),
                'total_joins': sum(len(p.joins) for p in procedures),
                'total_filters': sum(len(p.filters) for p in procedures)
            }
        }

    def _consolidate_tables_across_procedures(self, procedures: List[ProcedureAnalysis]) -> Dict[str, Any]:
        """Consolidate table usage across ALL procedures - one row per table"""

        table_consolidation = {}

        # Get all unique tables
        all_tables = set()
        for proc in procedures:
            all_tables.update(proc.tables.keys())

        # For each table, consolidate information from all procedures
        for table_name in all_tables:
            # Find all procedures that use this table
            using_procedures = []
            all_columns = set()
            all_usage_contexts = set()
            all_filters = []
            all_joins = []

            for proc in procedures:
                if table_name in proc.tables:
                    using_procedures.append(proc.procedure_id)
                    table_ref = proc.tables[table_name]
                    all_columns.update(table_ref.columns)
                    all_usage_contexts.update(table_ref.usage_contexts)

                # Collect filters for this table
                table_filters = [f for f in proc.filters if f.table == table_name]
                all_filters.extend(table_filters)

                # Collect joins for this table
                table_joins = [j for j in proc.joins if j.left_table == table_name or j.right_table == table_name]
                all_joins.extend(table_joins)

            # Consolidate filter conditions
            consolidated_filters = self._consolidate_filter_conditions(all_filters)

            # Consolidate join conditions
            consolidated_joins = self._consolidate_join_conditions(all_joins, table_name)

            # Create consolidated entry
            table_consolidation[table_name] = {
                'table_name': table_name,
                'used_in_procedures': using_procedures,
                'procedure_count': len(using_procedures),
                'all_columns': sorted(list(all_columns)),
                'usage_contexts': sorted(list(all_usage_contexts)),
                'consolidated_filters': consolidated_filters,
                'consolidated_joins': consolidated_joins,
                'table_purpose': self.sap_table_patterns.get(table_name, 'Business data table'),
                'migration_priority': self._assess_migration_priority(len(using_procedures), len(all_filters), len(all_joins))
            }

        return table_consolidation

    def _consolidate_filter_conditions(self, filters: List[FilterCondition]) -> Dict[str, Any]:
        """Consolidate filter conditions for a table across procedures"""

        if not filters:
            return {'has_filters': False, 'patterns': []}

        # Group by column
        column_filters = defaultdict(list)
        for f in filters:
            column_filters[f.column].append(f)

        patterns = []
        for column, column_filter_list in column_filters.items():
            # Analyze pattern for this column
            procedures_with_filter = set(f.procedure_id for f in column_filter_list)
            all_values = []
            operators = set()

            for f in column_filter_list:
                all_values.extend(f.values)
                operators.add(f.operator)

            unique_values = set(all_values)

            pattern = {
                'column': column,
                'procedures_with_filter': list(procedures_with_filter),
                'filter_frequency': len(procedures_with_filter),
                'operators': list(operators),
                'unique_values': list(unique_values),
                'example_conditions': [f.condition_text for f in column_filter_list[:3]],  # First 3 examples
                'migration_note': self._generate_filter_migration_note(len(procedures_with_filter), unique_values)
            }
            patterns.append(pattern)

        return {
            'has_filters': True,
            'total_filter_patterns': len(patterns),
            'patterns': patterns
        }

    def _consolidate_join_conditions(self, joins: List[JoinRelationship], table_name: str) -> Dict[str, Any]:
        """Consolidate join conditions for a table across procedures"""

        if not joins:
            return {'has_joins': False, 'relationships': []}

        # Group by related table
        join_relationships = defaultdict(list)
        for j in joins:
            related_table = j.right_table if j.left_table == table_name else j.left_table
            join_relationships[related_table].append(j)

        relationships = []
        for related_table, join_list in join_relationships.items():
            # Analyze relationship pattern
            procedures_with_join = set(j.procedure_id for j in join_list)
            join_types = set(j.join_type for j in join_list)
            conditions = set(j.condition for j in join_list)

            relationship = {
                'related_table': related_table,
                'procedures_with_join': list(procedures_with_join),
                'join_frequency': len(procedures_with_join),
                'join_types': list(join_types),
                'unique_conditions': list(conditions),
                'example_condition': join_list[0].condition,  # First example
                'migration_note': self._generate_join_migration_note(len(procedures_with_join), len(conditions))
            }
            relationships.append(relationship)

        return {
            'has_joins': True,
            'total_relationships': len(relationships),
            'relationships': relationships
        }

    def _generate_filter_migration_note(self, procedure_count: int, unique_values: set) -> str:
        """Generate migration note for filter patterns"""

        if procedure_count == 1:
            return "SINGLE_USE - Consider if filter is needed in migration"
        elif len(unique_values) == 1:
            return "CONSISTENT - Same filter across procedures, safe to retain"
        elif len(unique_values) > 5:
            return "VARIABLE - Multiple filter values, review for optimization"
        else:
            return "MODERATE - Few variations, consolidate if possible"

    def _generate_join_migration_note(self, procedure_count: int, condition_count: int) -> str:
        """Generate migration note for join patterns"""

        if procedure_count == 1:
            return "SINGLE_USE - Verify if join is critical for migration"
        elif condition_count == 1:
            return "CONSISTENT - Same join logic, retain as-is"
        else:
            return "VARIABLE - Multiple join patterns, standardize for migration"

    def _assess_migration_priority(self, procedure_count: int, filter_count: int, join_count: int) -> str:
        """Assess migration priority for a table"""

        score = procedure_count * 2 + filter_count + join_count

        if score >= 10:
            return "CRITICAL"
        elif score >= 5:
            return "HIGH"
        elif score >= 2:
            return "MEDIUM"
        else:
            return "LOW"

    def _analyze_filter_patterns(self, procedures: List[ProcedureAnalysis]) -> Dict[str, Any]:
        """Analyze filter patterns across procedures to identify migration requirements"""

        # First, identify all table.column combinations used across procedures
        all_table_columns = set()
        for proc in procedures:
            for table_name, table_ref in proc.tables.items():
                for column in table_ref.columns:
                    all_table_columns.add(f"{table_name}.{column}")

        # Group filters by table.column
        column_filters = defaultdict(list)
        column_usage = defaultdict(set)  # Track which procedures use each column

        for proc in procedures:
            # Track column usage (with or without filters)
            for table_name, table_ref in proc.tables.items():
                for column in table_ref.columns:
                    key = f"{table_name}.{column}"
                    column_usage[key].add(proc.procedure_id)

            # Track filters
            for filter_cond in proc.filters:
                key = f"{filter_cond.table}.{filter_cond.column}"
                column_filters[key].append({
                    'procedure': proc.procedure_id,
                    'operator': filter_cond.operator,
                    'values': filter_cond.values,
                    'condition': filter_cond.condition_text,
                    'is_parameter': filter_cond.is_parameter
                })

        # Analyze patterns
        filter_patterns = {}
        migration_recommendations = []

        for column in all_table_columns:
            filters = column_filters.get(column, [])
            procedures_using_column = column_usage.get(column, set())
            procedures_with_filters = set(f['procedure'] for f in filters)
            procedures_without_filters = procedures_using_column - procedures_with_filters

            if not filters:
                continue  # Skip columns with no filters

            # Analyze value patterns
            all_values = []
            operators = set()
            has_parameters = False

            for f in filters:
                all_values.extend(f['values'])
                operators.add(f['operator'])
                if f['is_parameter']:
                    has_parameters = True

            unique_values = set(all_values)
            value_frequency = Counter(all_values)

            pattern_analysis = {
                'column': column,
                'procedures_using_column': list(procedures_using_column),
                'procedures_with_filters': list(procedures_with_filters),
                'procedures_without_filters': list(procedures_without_filters),
                'total_procedures_using': len(procedures_using_column),
                'filtered_procedures_count': len(procedures_with_filters),
                'unfiltered_procedures_count': len(procedures_without_filters),
                'operators_used': list(operators),
                'unique_values': list(unique_values),
                'value_frequency': dict(value_frequency),
                'has_parameters': has_parameters,
                'filters': filters
            }

            # Enhanced migration recommendation logic
            if procedures_without_filters:
                # Some procedures use the column without filters - they need ALL values
                recommendation = f"Column {column}: {len(procedures_with_filters)} procedures filter it " \
                               f"({', '.join(sorted(unique_values))}), but {len(procedures_without_filters)} procedures " \
                               f"({', '.join(sorted(procedures_without_filters))}) use it without filters. " \
                               f"RECOMMENDATION: Remove filter, use ALL values for migration."
                migration_recommendations.append(recommendation)
                pattern_analysis['migration_decision'] = 'REMOVE_FILTER'
            elif len(unique_values) == 1:
                # All procedures use the same filter value
                recommendation = f"Column {column}: All {len(procedures_with_filters)} procedures consistently filter " \
                               f"for '{list(unique_values)[0]}'. RECOMMENDATION: Retain filter in migration."
                migration_recommendations.append(recommendation)
                pattern_analysis['migration_decision'] = 'RETAIN_FILTER'
            else:
                # Multiple filter values across procedures
                recommendation = f"Column {column}: {len(procedures_with_filters)} procedures use different filters " \
                               f"({', '.join(sorted(unique_values))}). RECOMMENDATION: Consolidate or use union of values."
                migration_recommendations.append(recommendation)
                pattern_analysis['migration_decision'] = 'CONSOLIDATE_FILTER'

            filter_patterns[column] = pattern_analysis

        return {
            'patterns': filter_patterns,
            'recommendations': migration_recommendations,
            'summary': {
                'total_filtered_columns': len(filter_patterns),
                'columns_needing_all_values': len([p for p in filter_patterns.values() if p['migration_decision'] == 'REMOVE_FILTER']),
                'columns_with_consistent_filters': len([p for p in filter_patterns.values() if p['migration_decision'] == 'RETAIN_FILTER'])
            }
        }

    def _analyze_join_patterns(self, procedures: List[ProcedureAnalysis]) -> Dict[str, Any]:
        """Analyze join patterns for migration complexity assessment"""

        # Collect all joins
        all_joins = []
        for proc in procedures:
            all_joins.extend(proc.joins)

        # Group joins by table pairs
        join_patterns = defaultdict(list)

        for join in all_joins:
            # Create a consistent key for table pairs
            tables = sorted([join.left_table, join.right_table])
            key = f"{tables[0]}-{tables[1]}"

            join_patterns[key].append({
                'procedure': join.procedure_id,
                'join_type': join.join_type,
                'condition': join.condition,
                'left_table': join.left_table,
                'right_table': join.right_table,
                'left_columns': join.left_columns,
                'right_columns': join.right_columns
            })

        # Analyze patterns
        pattern_analysis = {}
        migration_complexity = []

        for table_pair, joins in join_patterns.items():
            # Analyze join consistency
            join_types = set(j['join_type'] for j in joins)
            conditions = set(j['condition'] for j in joins)
            proc_count = len(set(j['procedure'] for j in joins))

            analysis = {
                'table_pair': table_pair,
                'used_in_procedures': proc_count,
                'join_types': list(join_types),
                'unique_conditions': len(conditions),
                'conditions': list(conditions),
                'joins': joins
            }

            # Complexity assessment
            if len(join_types) > 1:
                migration_complexity.append(f"Table pair {table_pair} uses different join types: {join_types}")

            if len(conditions) > 1:
                migration_complexity.append(f"Table pair {table_pair} has {len(conditions)} different join conditions")

            pattern_analysis[table_pair] = analysis

        return {
            'patterns': pattern_analysis,
            'complexity_issues': migration_complexity,
            'summary': {
                'total_join_patterns': len(join_patterns),
                'avg_procedures_per_join': sum(p['used_in_procedures'] for p in pattern_analysis.values()) / len(pattern_analysis) if pattern_analysis else 0
            }
        }

    def _create_data_lineage(self, procedures: List[ProcedureAnalysis]) -> Dict[str, Any]:
        """Create data lineage graph for migration planning"""

        # Create directed graph
        G = nx.DiGraph()

        # Add nodes (tables)
        all_tables = set()
        for proc in procedures:
            all_tables.update(proc.tables.keys())

        for table in all_tables:
            G.add_node(table,
                      table_type=self.sap_table_patterns.get(table, 'Business Table'),
                      procedures=[]
                      )

        # Add edges (joins)
        for proc in procedures:
            # Add procedure to table nodes
            for table in proc.tables.keys():
                G.nodes[table]['procedures'].append(proc.procedure_id)

            # Add join relationships
            for join in proc.joins:
                if join.left_table in G.nodes and join.right_table in G.nodes:
                    if G.has_edge(join.left_table, join.right_table):
                        # Update existing edge
                        G.edges[join.left_table, join.right_table]['procedures'].append(proc.procedure_id)
                        G.edges[join.left_table, join.right_table]['join_types'].add(join.join_type)
                    else:
                        # Create new edge
                        G.add_edge(join.left_table, join.right_table,
                                 procedures=[proc.procedure_id],
                                 join_types={join.join_type},
                                 conditions=[join.condition])

        # Analyze graph properties
        centrality = nx.degree_centrality(G)
        components = list(nx.weakly_connected_components(G))

        return {
            'nodes': list(G.nodes(data=True)),
            'edges': list(G.edges(data=True)),
            'centrality': centrality,
            'components': [list(comp) for comp in components],
            'summary': {
                'total_tables': len(G.nodes),
                'total_relationships': len(G.edges),
                'connected_components': len(components),
                'most_connected_tables': sorted(centrality.items(), key=lambda x: x[1], reverse=True)[:5]
            }
        }

    def _identify_critical_migration_points(self, procedures: List[ProcedureAnalysis]) -> List[Dict[str, Any]]:
        """Identify critical points for migration planning"""

        critical_points = []

        # High complexity procedures
        high_complexity = [p for p in procedures if p.complexity_score > 50]
        if high_complexity:
            critical_points.append({
                'type': 'High Complexity Procedures',
                'description': f'{len(high_complexity)} procedures have high complexity scores',
                'procedures': [p.procedure_id for p in high_complexity],
                'recommendation': 'Prioritize testing and validation for these procedures'
            })

        # Tables used across many procedures
        table_usage = defaultdict(int)
        for proc in procedures:
            for table in proc.tables.keys():
                table_usage[table] += 1

        highly_used_tables = [(table, count) for table, count in table_usage.items() if count > len(procedures) * 0.5]
        if highly_used_tables:
            critical_points.append({
                'type': 'Highly Used Tables',
                'description': f'Tables used in >50% of procedures',
                'tables': highly_used_tables,
                'recommendation': 'Ensure these tables are migrated first and thoroughly tested'
            })

        return critical_points
    
    def parse_procedure(self, sql_text: str, procedure_id: str = None) -> ProcedureAnalysis:
        """Parse a single SAP HANA procedure using ANTLR or sqlparse fallback"""

        # Extract procedure ID if not provided
        if not procedure_id:
            procedure_id = self._extract_procedure_id(sql_text)

        # Try ANTLR parsing first, fallback to sqlparse
        if self.use_antlr:
            try:
                return self._parse_with_antlr(sql_text, procedure_id)
            except Exception as e:
                print(f"⚠️ ANTLR parsing failed for {procedure_id}, using sqlparse fallback: {e}")
                return self._parse_with_sqlparse(sql_text, procedure_id)
        else:
            return self._parse_with_sqlparse(sql_text, procedure_id)

    def _parse_with_antlr(self, sql_text: str, procedure_id: str) -> ProcedureAnalysis:
        """Parse using ANTLR SAP HANA grammar"""

        # Create ANTLR input stream
        input_stream = InputStream(sql_text)
        lexer = HanaLexer(input_stream)
        stream = CommonTokenStream(lexer)
        parser = HanaParser(stream)

        # Parse the SQL
        tree = parser.compilationUnit()  # Adjust based on your grammar's start rule

        # Walk the parse tree
        listener = SAP_HANA_Listener()
        walker = ParseTreeWalker()
        walker.walk(listener, tree)

        # Convert ANTLR results to our format
        tables = {}
        for table_name in listener.tables:
            tables[table_name] = TableReference(
                name=table_name,
                columns=listener.columns.get(table_name, set()),
                usage_contexts={'ANTLR_PARSED'}
            )

        # For now, use regex fallback for filters and joins
        # TODO: Implement proper ANTLR extraction for these
        cleaned_sql = self._clean_sql(sql_text)
        filters = self._extract_filters(cleaned_sql, procedure_id)
        joins = self._extract_joins(cleaned_sql, procedure_id)
        parameters = self._extract_parameters(cleaned_sql)

        # Calculate complexity
        complexity = self._calculate_complexity(tables, filters, joins)

        return ProcedureAnalysis(
            procedure_id=procedure_id,
            tables=tables,
            filters=filters,
            joins=joins,
            parameters=parameters,
            raw_sql=sql_text,
            complexity_score=complexity
        )

    def _parse_with_sqlparse(self, sql_text: str, procedure_id: str) -> ProcedureAnalysis:
        """Parse using sqlparse fallback"""

        # Clean and normalize SQL
        cleaned_sql = self._clean_sql(sql_text)

        # Parse SQL using sqlparse
        parsed = sqlparse.parse(cleaned_sql)[0] if sqlparse.parse(cleaned_sql) else None

        # Extract components
        tables = self._extract_tables(cleaned_sql)
        filters = self._extract_filters(cleaned_sql, procedure_id)
        joins = self._extract_joins(cleaned_sql, procedure_id)
        parameters = self._extract_parameters(cleaned_sql)

        # Calculate complexity
        complexity = self._calculate_complexity(tables, filters, joins)

        return ProcedureAnalysis(
            procedure_id=procedure_id,
            tables=tables,
            filters=filters,
            joins=joins,
            parameters=parameters,
            raw_sql=sql_text,
            complexity_score=complexity
        )
    
    def _extract_procedure_id(self, sql_text: str) -> str:
        """Extract procedure ID from SQL text"""
        # Pattern for SAP HANA procedure names
        patterns = [
            r'PROCEDURE\s+"[^"]*"\.?"([^"]+)"',
            r'PROCEDURE\s+([A-Z_][A-Z0-9_]*)',
            r'::([A-Z_][A-Z0-9_]*)"'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, sql_text, re.IGNORECASE)
            if match:
                return match.group(1)
        
        return "UNKNOWN_PROCEDURE"
    
    def _clean_sql(self, sql_text: str) -> str:
        """Clean and normalize SQL text - preserve filter values"""
        # Remove comments
        sql_text = re.sub(r'/\*.*?\*/', '', sql_text, flags=re.DOTALL)
        sql_text = re.sub(r'--.*?\n', '\n', sql_text)

        # DON'T remove string literals - we need them for filter analysis
        # sql_text = re.sub(r"'[^']*'", "'STRING'", sql_text)

        # Normalize whitespace
        sql_text = re.sub(r'\s+', ' ', sql_text)

        return sql_text
    
    def _extract_tables(self, sql_text: str) -> Dict[str, TableReference]:
        """Extract all table references with detailed analysis"""
        tables = {}
        
        # Pattern for table references in different contexts
        patterns = [
            # FROM clause
            (r'\bFROM\s+(?:"[^"]+"\.)?"?([A-Z][A-Z0-9_]+)"?(?:\s+(?:AS\s+)?([A-Z][A-Z0-9_]+))?', 'FROM'),
            # JOIN clause
            (r'\b(?:INNER|LEFT|RIGHT|FULL|CROSS)?\s*JOIN\s+(?:"[^"]+"\.)?"?([A-Z][A-Z0-9_]+)"?(?:\s+(?:AS\s+)?([A-Z][A-Z0-9_]+))?', 'JOIN'),
            # INSERT INTO
            (r'\bINSERT\s+INTO\s+(?:"[^"]+"\.)?"?([A-Z][A-Z0-9_]+)"?', 'INSERT'),
            # UPDATE
            (r'\bUPDATE\s+(?:"[^"]+"\.)?"?([A-Z][A-Z0-9_]+)"?', 'UPDATE'),
        ]
        
        for pattern, context in patterns:
            matches = re.findall(pattern, sql_text, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    table_name = match[0]
                    alias = match[1] if len(match) > 1 and match[1] else None
                else:
                    table_name = match
                    alias = None
                
                if table_name and len(table_name) >= 3:
                    if table_name not in tables:
                        tables[table_name] = TableReference(
                            name=table_name,
                            alias=alias
                        )
                    tables[table_name].usage_contexts.add(context)
        
        # Extract columns for each table
        for table_name, table_ref in tables.items():
            table_ref.columns = self._extract_columns_for_table(sql_text, table_name, table_ref.alias)
        
        return tables
    
    def _extract_columns_for_table(self, sql_text: str, table_name: str, alias: str = None) -> Set[str]:
        """Extract columns used for a specific table"""
        columns = set()
        
        # Patterns for column references
        identifiers = [table_name]
        if alias:
            identifiers.append(alias)
        
        for identifier in identifiers:
            pattern = rf'\b{identifier}\.([A-Z][A-Z0-9_]*)\b'
            matches = re.findall(pattern, sql_text, re.IGNORECASE)
            columns.update(match.upper() for match in matches)
        
        return columns
    
    def _extract_filters(self, sql_text: str, procedure_id: str) -> List[FilterCondition]:
        """Extract filter conditions with detailed analysis"""
        filters = []
        
        # Find WHERE clauses
        where_pattern = r'\bWHERE\b(.*?)(?:\bGROUP\s+BY\b|\bORDER\s+BY\b|\bHAVING\b|\bUNION\b|\bINTERSECT\b|\bEXCEPT\b|\;|$)'
        where_matches = re.findall(where_pattern, sql_text, re.IGNORECASE | re.DOTALL)
        
        for where_clause in where_matches:
            # Extract individual conditions
            conditions = self._parse_where_conditions(where_clause, procedure_id)
            filters.extend(conditions)
        
        # Find HAVING clauses
        having_pattern = r'\bHAVING\b(.*?)(?:\bORDER\s+BY\b|\bUNION\b|\bINTERSECT\b|\bEXCEPT\b|\;|$)'
        having_matches = re.findall(having_pattern, sql_text, re.IGNORECASE | re.DOTALL)
        
        for having_clause in having_matches:
            conditions = self._parse_where_conditions(having_clause, procedure_id)
            filters.extend(conditions)
        
        return filters
    
    def _parse_where_conditions(self, where_clause: str, procedure_id: str) -> List[FilterCondition]:
        """Parse individual WHERE conditions"""
        conditions = []
        
        # Split by AND/OR but preserve the conditions
        condition_parts = re.split(r'\b(?:AND|OR)\b', where_clause, flags=re.IGNORECASE)
        
        for part in condition_parts:
            part = part.strip()
            if not part:
                continue
            
            # Pattern for table.column operator value
            condition_pattern = r'([A-Z][A-Z0-9_]*\.[A-Z][A-Z0-9_]*)\s*([<>=!]+|IN|LIKE|BETWEEN)\s*(.+?)(?:\s+AND\s+|\s+OR\s+|$)'
            match = re.search(condition_pattern, part, re.IGNORECASE)
            
            if match:
                table_column = match.group(1)
                operator = match.group(2)
                value_part = match.group(3).strip()
                
                # Split table.column
                if '.' in table_column:
                    table, column = table_column.split('.', 1)
                    
                    # Extract values
                    values = self._extract_filter_values(value_part, operator)
                    
                    # Check if it's a parameter
                    is_parameter = ':' in value_part or value_part.startswith('?')
                    
                    condition = FilterCondition(
                        table=table.upper(),
                        column=column.upper(),
                        operator=operator.upper(),
                        values=values,
                        condition_text=part.strip(),
                        procedure_id=procedure_id,
                        is_parameter=is_parameter
                    )
                    conditions.append(condition)
        
        return conditions
    
    def _extract_filter_values(self, value_part: str, operator: str) -> List[str]:
        """Extract filter values from condition - preserve actual values"""
        values = []

        if operator.upper() == 'IN':
            # Extract values from IN clause - preserve quotes to get actual values
            in_pattern = r'\((.*?)\)'
            match = re.search(in_pattern, value_part)
            if match:
                values_str = match.group(1)
                # Split by comma and clean, but preserve the actual values
                raw_values = [v.strip() for v in values_str.split(',')]
                for v in raw_values:
                    # Remove quotes but keep the actual value
                    clean_value = v.strip("'\"").strip()
                    if clean_value:  # Only add non-empty values
                        values.append(clean_value)
        elif operator.upper() == 'BETWEEN':
            # Extract BETWEEN values
            between_pattern = r'(.+?)\s+AND\s+(.+)'
            match = re.search(between_pattern, value_part, re.IGNORECASE)
            if match:
                val1 = match.group(1).strip().strip("'\"").strip()
                val2 = match.group(2).strip().strip("'\"").strip()
                values = [val1, val2]
        else:
            # Single value - preserve actual value
            clean_value = value_part.strip().strip("'\"").strip()
            if clean_value:
                values = [clean_value]

        return values
    
    def _extract_joins(self, sql_text: str, procedure_id: str) -> List[JoinRelationship]:
        """Extract join relationships with detailed analysis"""
        joins = []
        
        # Pattern for JOIN clauses
        join_pattern = r'\b((?:INNER|LEFT|RIGHT|FULL|CROSS)?\s*JOIN)\s+(?:"[^"]+"\.)?"?([A-Z][A-Z0-9_]+)"?(?:\s+(?:AS\s+)?([A-Z][A-Z0-9_]+))?\s+ON\s+(.*?)(?=\s+(?:INNER|LEFT|RIGHT|FULL|CROSS)?\s*JOIN|\bWHERE\b|\bGROUP\s+BY\b|\bORDER\s+BY\b|\bHAVING\b|\;|$)'
        
        matches = re.findall(join_pattern, sql_text, re.IGNORECASE | re.DOTALL)
        
        for match in matches:
            join_type = match[0].strip()
            right_table = match[1]
            right_alias = match[2] if match[2] else None
            condition = match[3].strip()
            
            # Parse join condition to extract left table
            left_table = self._extract_left_table_from_join(condition)
            
            # Extract columns involved in join
            left_columns, right_columns = self._extract_join_columns(condition)
            
            join_rel = JoinRelationship(
                left_table=left_table,
                right_table=right_table,
                join_type=join_type,
                condition=condition,
                procedure_id=procedure_id,
                left_columns=left_columns,
                right_columns=right_columns
            )
            joins.append(join_rel)
        
        return joins
    
    def _extract_left_table_from_join(self, condition: str) -> str:
        """Extract left table from join condition"""
        # Look for table.column pattern on the left side of =
        pattern = r'([A-Z][A-Z0-9_]*)\.[A-Z][A-Z0-9_]*\s*='
        match = re.search(pattern, condition, re.IGNORECASE)
        return match.group(1).upper() if match else "UNKNOWN"
    
    def _extract_join_columns(self, condition: str) -> Tuple[List[str], List[str]]:
        """Extract columns involved in join condition"""
        left_columns = []
        right_columns = []
        
        # Pattern for table.column = table.column
        pattern = r'([A-Z][A-Z0-9_]*\.[A-Z][A-Z0-9_]*)\s*=\s*([A-Z][A-Z0-9_]*\.[A-Z][A-Z0-9_]*)'
        matches = re.findall(pattern, condition, re.IGNORECASE)
        
        for match in matches:
            left_col = match[0].split('.')[1] if '.' in match[0] else match[0]
            right_col = match[1].split('.')[1] if '.' in match[1] else match[1]
            left_columns.append(left_col.upper())
            right_columns.append(right_col.upper())
        
        return left_columns, right_columns
    
    def _extract_parameters(self, sql_text: str) -> List[Dict[str, Any]]:
        """Extract procedure parameters"""
        parameters = []
        
        # Pattern for procedure parameters
        param_pattern = r'\bIN\s+([A-Z_][A-Z0-9_]*)\s+([A-Z]+(?:\([^)]+\))?)'
        matches = re.findall(param_pattern, sql_text, re.IGNORECASE)
        
        for match in matches:
            param = {
                'name': match[0],
                'type': match[1],
                'direction': 'IN'
            }
            parameters.append(param)
        
        return parameters
    
    def _calculate_complexity(self, tables: Dict[str, TableReference], 
                            filters: List[FilterCondition], 
                            joins: List[JoinRelationship]) -> int:
        """Calculate procedure complexity score"""
        score = 0
        score += len(tables) * 2  # Tables
        score += len(filters) * 3  # Filters
        score += len(joins) * 5   # Joins
        
        # Bonus for complex joins
        for join in joins:
            if 'AND' in join.condition:
                score += 2
        
        return score
