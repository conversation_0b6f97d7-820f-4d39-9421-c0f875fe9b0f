#!/usr/bin/env python3
"""
Test script for SAP HANA SQL Analyzer
=====================================

Quick test to verify all components are working correctly.
"""

def test_imports():
    """Test all imports"""
    print("🔍 Testing imports...")
    
    try:
        from config import Config
        print("✅ Config imported")
        
        from sql_parser import AdvancedSQLParser
        print("✅ SQL Parser imported")
        
        from llm_service import LLMService
        print("✅ LLM Service imported")
        
        import app
        print("✅ Main app imported")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_sql_parser():
    """Test SQL parser with sample procedure"""
    print("\n🔍 Testing SQL parser...")
    
    try:
        from sql_parser import AdvancedSQLParser
        
        parser = AdvancedSQLParser()
        
        # Sample SQL procedure
        sample_sql = '''
        PROCEDURE "TEST_PROC" ( IN IP_ERDAT NVARCHAR(8) )
        AS
        BEGIN
        SELECT VBAK.VBELN, VBAP.POSNR
        FROM VBAK AS VBAK
        INNER JOIN VBAP AS VBAP ON VBAK.VBELN = VBAP.VBELN
        WHERE VBAK.VBTYP = 'C'
        AND VBAP.ERDAT > :IP_ERDAT;
        END;
        '''
        
        # Parse the procedure
        result = parser.parse_procedure(sample_sql, "TEST_PROC")
        
        print(f"✅ Parsed procedure: {result.procedure_id}")
        print(f"   Tables found: {len(result.tables)}")
        print(f"   Filters found: {len(result.filters)}")
        print(f"   Joins found: {len(result.joins)}")
        print(f"   Complexity score: {result.complexity_score}")
        
        return True
        
    except Exception as e:
        print(f"❌ SQL parser error: {e}")
        return False

def test_llm_service():
    """Test LLM service initialization"""
    print("\n🔍 Testing LLM service...")
    
    try:
        from llm_service import LLMService
        
        llm_service = LLMService()
        print("✅ LLM service initialized")
        
        # Test configuration
        if llm_service.config.AZURE_OPENAI_API_KEY:
            print("✅ Azure OpenAI configured")
        else:
            print("⚠️ Azure OpenAI not configured")
        
        if llm_service.config.GEMINI_API_KEY:
            print("✅ Gemini configured")
        else:
            print("⚠️ Gemini not configured")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM service error: {e}")
        return False

def test_plotly_fix():
    """Test Plotly visualization fix"""
    print("\n🔍 Testing Plotly fix...")
    
    try:
        import plotly.express as px
        
        # Create a simple chart
        fig = px.bar(x=['A', 'B', 'C'], y=[1, 2, 3], title="Test Chart")
        
        # Test the fix
        fig.update_layout(xaxis_tickangle=45)
        print("✅ Plotly update_layout works")
        
        return True
        
    except Exception as e:
        print(f"❌ Plotly error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 SAP HANA SQL Analyzer - Component Tests")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_sql_parser,
        test_llm_service,
        test_plotly_fix
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Ready to run the application.")
        print("\n🚀 To start the app:")
        print("   python run.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
