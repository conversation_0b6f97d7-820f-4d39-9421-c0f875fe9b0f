Procedure ID,Column Name,Used As,Filter Condition,Join Condition,Notes
PR_GS_O2C_ADDRC_DELTA,"CDHDR.MANDANT, CDHDR.OBJECTCLAS, CDHDR.OBJECTID, CDHDR.CHANGENR, CDHDR.UDATE, CDHDR.UTIME, CDHDR.USERNAME, CDHD<PERSON>.TCODE","SELECT, JOIN",CDHDR.OBJECTCLAS = 'BELEG',CDPOS.MANDANT = CDHDR.MANDANT AND CDPOS.OBJECTCLAS = CDHDR.OBJECTCLAS AND CDPOS.OBJECTID = CDHDR.OBJECTID AND CDPOS.CHANGENR = CDHDR.CHANGENR,Change document header
PR_GS_O2C_ADDRC_DELTA,"CDPOS.MANDANT, CDPOS.OBJECTCLAS, CDPOS.OBJECTID, CDPOS.CHANGENR, CDPOS.FNAME, CDPOS.TABNAME, CDPOS.VALUE_OLD, CDPOS.VALUE_NEW, CDPOS.TABKEY","SELECT, JOIN",CDPOS.FNAME = 'RSTGR' AND CDPOS.TABNAME = 'BSEG',CDPOS.TABKEY = BSEG_INV.MANDT || BSEG_INV.BUKRS || BSEG_INV.BELNR || BSEG_INV.GJAHR || BSEG_INV.BUZEI,Change document items
PR_GS_O2C_ADDRC_DELTA,"BSEG_INV.MANDT, BSEG_INV.BUKRS, BSEG_INV.BELNR, BSEG_INV.GJAHR, BSEG_INV.BUZEI, BSEG_INV.BSCHL, BSEG_INV.KOART",JOIN,"BSEG_INV.BSCHL IN ('01','11') AND BSEG_INV.KOART = 'D'",BSEG_INV.MANDT = CDPOS.MANDANT AND BSEG_INV.MANDT = BKPF_INV.MANDT AND BSEG_INV.BUKRS = BKPF_INV.BUKRS AND BSEG_INV.BELNR = BKPF_INV.BELNR AND BSEG_INV.GJAHR = BKPF_INV.GJAHR,Accounting document line items
PR_GS_O2C_ADDRC_DELTA,"BKPF_INV.MANDT, BKPF_INV.BUKRS, BKPF_INV.BELNR, BKPF_INV.GJAHR, BKPF_INV.AWTYP, BKPF_INV.AWKEY","SELECT, JOIN",BKPF_INV.AWTYP = 'VBRK',VBRK.MANDT = BKPF_INV.MANDT AND VBRK.VBELN = BKPF_INV.AWKEY,Accounting document header
PR_GS_O2C_ADDRC_DELTA,"VBRK.MANDT, VBRK.VBELN",JOIN,,VBRP.MANDT = VBRK.MANDT AND VBRP.VBELN = VBRK.VBELN,Billing document header
PR_GS_O2C_ADDRC_DELTA,"VBRP.MANDT, VBRP.VBELN, VBRP.AUBEL, VBRP.AUPOS",JOIN,,VBRP.AUBEL = VBAP.VBELN AND VBRP.AUPOS = VBAP.POSNR,Billing document items
PR_GS_O2C_ADDRC_DELTA,"VBAP.MANDT, VBAP.VBELN, VBAP.POSNR","SELECT, JOIN",,VBAK.VBELN = VBAP.VBELN,Sales document items
PR_GS_O2C_ADDRC_DELTA,"VBAK.VBELN, VBAK.VBTYP",JOIN,VBAK.VBTYP = 'C',VBAK.VBELN = VBAP.VBELN,Sales document header
PR_GS_O2C_ADDRC_DELTA,"USR02.MANDT, USR02.BNAME, USR02.USTYP","SELECT, LEFT JOIN",,USR02.MANDT = CDHDR.MANDANT AND USR02.BNAME = CDHDR.USERNAME,User master record
PR_GS_O2C_ADDRC_DELTA,"USH02.MANDT, USH02.BNAME, USH02.USTYP","SELECT, LEFT JOIN",,USH02.MANDT = CDHDR.MANDANT AND USH02.BNAME = CDHDR.USERNAME,User master record (historical)
PR_GS_O2C_ADDRC_DELTA,"CHKSUM.PARTITION_ID, CHKSUM.DATE","SELECT, LEFT JOIN",CHKSUM.DATE = :IP_ERDAT,,Checksum table for delta processing
PR_GS_O2C_ADDRC_DELTA,"USER_AT.EXTENSIONATTRIBUTE10, USER_AT.DEPARTMENT","SELECT, LEFT JOIN",department <> '' AND EXTENSIONATTRIBUTE10 <> '',USER_AT.EXTENSIONATTRIBUTE10 = CDHDR.USERNAME,User attributes extension table
