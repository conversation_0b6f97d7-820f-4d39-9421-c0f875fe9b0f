"""
SAP HANA SQL Analyzer - Streamlit Web Application
================================================

A comprehensive web application for analyzing SAP HANA SQL procedures
and generating migration requirements documentation using LLMs.

Features:
- Upload and analyze multiple SQL procedures
- Advanced SQL parsing with migration insights
- Dual LLM support (Azure OpenAI + Google Gemini)
- Interactive visualizations and reports
- CSV and text file downloads
- Real-time analysis progress tracking
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import io
import zipfile
from datetime import datetime
from typing import List, Dict, Any, Optional

# Import our modules
from config import Config
from sql_parser import AdvancedSQLParser, ProcedureAnalysis
from llm_service import LLMService, LLMResponse

# Page configuration
st.set_page_config(
    page_title=Config.PAGE_TITLE,
    page_icon=Config.PAGE_ICON,
    layout=Config.LAYOUT,
    initial_sidebar_state="expanded"
)

class SAP_HANA_Analyzer_App:
    """Main Streamlit application class"""
    
    def __init__(self):
        self.config = Config()
        self.sql_parser = AdvancedSQLParser()
        self.llm_service = LLMService()
        
        # Initialize session state
        if 'analysis_results' not in st.session_state:
            st.session_state.analysis_results = None
        if 'llm_response' not in st.session_state:
            st.session_state.llm_response = None
        if 'procedures_data' not in st.session_state:
            st.session_state.procedures_data = []
    
    def run(self):
        """Main application entry point"""
        
        # Sidebar configuration
        self._render_sidebar()
        
        # Main content
        st.title("🔍 SAP HANA SQL Analyzer")
        st.markdown("**Advanced SQL Procedure Analysis for Migration Planning**")
        
        # Configuration validation
        config_issues = self.config.validate_config()
        if config_issues:
            st.warning("⚠️ Configuration Issues:")
            for issue in config_issues:
                st.write(f"- {issue}")
            st.info("Please check your API keys in the sidebar.")
        
        # Main tabs
        tab1, tab2, tab3, tab4 = st.tabs([
            "📁 Upload & Analyze", 
            "📊 Analysis Results", 
            "🤖 LLM Requirements", 
            "📈 Visualizations"
        ])
        
        with tab1:
            self._render_upload_tab()
        
        with tab2:
            self._render_results_tab()
        
        with tab3:
            self._render_llm_tab()
        
        with tab4:
            self._render_visualizations_tab()
    
    def _render_sidebar(self):
        """Render sidebar with configuration options"""
        
        st.sidebar.header("⚙️ Configuration")
        
        # LLM Provider Selection
        st.sidebar.subheader("LLM Provider")
        provider = st.sidebar.selectbox(
            "Choose LLM Provider",
            options=["azure", "gemini"],
            index=0 if self.config.DEFAULT_LLM_PROVIDER == "azure" else 1,
            help="Select the LLM provider for requirements generation"
        )
        
        # Test LLM Connection
        if st.sidebar.button("🔍 Test LLM Connection"):
            with st.sidebar:
                with st.spinner(f"Testing {provider} connection..."):
                    response = self.llm_service.test_connection(provider)
                    if response.success:
                        st.success(f"✅ {provider.title()} connection successful!")
                    else:
                        st.error(f"❌ {provider.title()} connection failed: {response.error}")
        
        # API Configuration
        st.sidebar.subheader("API Configuration")
        
        if provider == "azure":
            azure_key = st.sidebar.text_input(
                "Azure OpenAI API Key",
                value=self.config.AZURE_OPENAI_API_KEY[:10] + "..." if self.config.AZURE_OPENAI_API_KEY else "",
                type="password",
                help="Your Azure OpenAI API key"
            )
            azure_model = st.sidebar.text_input(
                "Azure Model",
                value=self.config.AZURE_OPENAI_MODEL,
                help="Azure OpenAI model name"
            )
        else:
            gemini_key = st.sidebar.text_input(
                "Gemini API Key",
                value=self.config.GEMINI_API_KEY[:10] + "..." if self.config.GEMINI_API_KEY else "",
                type="password",
                help="Your Google Gemini API key"
            )
        
        # Analysis Settings
        st.sidebar.subheader("Analysis Settings")
        max_procedures = st.sidebar.number_input(
            "Max Procedures to Analyze",
            min_value=1,
            max_value=self.config.MAX_PROCEDURES,
            value=min(50, self.config.MAX_PROCEDURES),
            help="Maximum number of procedures to analyze in one batch"
        )
        
        # Store settings in session state
        st.session_state.selected_provider = provider
        st.session_state.max_procedures = max_procedures
        
        # Download Section
        st.sidebar.subheader("📥 Downloads")
        if st.session_state.analysis_results:
            self._render_download_buttons()
    
    def _render_upload_tab(self):
        """Render the upload and analysis tab"""
        
        st.header("📁 Upload SQL Procedures")
        
        # File upload options
        upload_method = st.radio(
            "Choose upload method:",
            ["Upload Files", "Paste SQL Text"],
            horizontal=True
        )
        
        procedures_data = []
        
        if upload_method == "Upload Files":
            uploaded_files = st.file_uploader(
                "Upload SQL procedure files",
                type=['sql', 'hdbprocedure', 'txt'],
                accept_multiple_files=True,
                help="Upload your SAP HANA SQL procedure files"
            )
            
            if uploaded_files:
                st.success(f"📁 {len(uploaded_files)} files uploaded")
                
                for file in uploaded_files:
                    try:
                        content = file.read().decode('utf-8')
                        procedures_data.append({
                            'name': file.name,
                            'content': content,
                            'size': len(content)
                        })
                    except Exception as e:
                        st.error(f"Error reading {file.name}: {e}")
        
        else:  # Paste SQL Text
            st.subheader("Paste SQL Procedure")
            
            procedure_name = st.text_input(
                "Procedure Name",
                placeholder="e.g., PR_GS_O2C_ADDRC_DELTA"
            )
            
            sql_content = st.text_area(
                "SQL Procedure Content",
                height=300,
                placeholder="Paste your SAP HANA SQL procedure here..."
            )
            
            if procedure_name and sql_content:
                procedures_data.append({
                    'name': procedure_name,
                    'content': sql_content,
                    'size': len(sql_content)
                })
        
        # Analysis section
        if procedures_data:
            st.subheader("🔍 Analysis")
            
            # Show procedure summary
            total_size = sum(p['size'] for p in procedures_data)
            st.info(f"📊 Ready to analyze {len(procedures_data)} procedures ({total_size:,} characters)")
            
            # Limit check
            max_procs = st.session_state.get('max_procedures', 50)
            if len(procedures_data) > max_procs:
                st.warning(f"⚠️ Too many procedures. Limiting to first {max_procs} procedures.")
                procedures_data = procedures_data[:max_procs]
            
            # Analysis button
            if st.button("🚀 Start Analysis", type="primary"):
                self._run_analysis(procedures_data)
    
    def _run_analysis(self, procedures_data: List[Dict[str, Any]]):
        """Run the SQL analysis"""
        
        with st.spinner("🔍 Analyzing SQL procedures..."):
            
            # Progress tracking
            progress_bar = st.progress(0)
            status_text = st.empty()
            
            try:
                # Parse procedures
                parsed_procedures = []
                
                for i, proc_data in enumerate(procedures_data):
                    status_text.text(f"Parsing procedure {i+1}/{len(procedures_data)}: {proc_data['name']}")
                    
                    try:
                        analysis = self.sql_parser.parse_procedure(
                            proc_data['content'], 
                            proc_data['name']
                        )
                        parsed_procedures.append(analysis)
                    except Exception as e:
                        st.error(f"Error parsing {proc_data['name']}: {e}")
                    
                    progress_bar.progress((i + 1) / len(procedures_data) * 0.7)
                
                # Cross-procedure analysis
                status_text.text("Performing cross-procedure analysis...")
                cross_analysis = self.sql_parser.analyze_cross_procedure_patterns(parsed_procedures)
                progress_bar.progress(0.9)
                
                # Store results
                st.session_state.analysis_results = {
                    'procedures': parsed_procedures,
                    'cross_analysis': cross_analysis,
                    'timestamp': datetime.now().isoformat()
                }
                st.session_state.procedures_data = procedures_data
                
                progress_bar.progress(1.0)
                status_text.text("✅ Analysis complete!")
                
                st.success(f"🎉 Successfully analyzed {len(parsed_procedures)} procedures!")
                
                # Show quick summary
                self._show_quick_summary(cross_analysis)
                
            except Exception as e:
                st.error(f"❌ Analysis failed: {e}")
                st.exception(e)
    
    def _show_quick_summary(self, cross_analysis: Dict[str, Any]):
        """Show quick analysis summary"""
        
        summary = cross_analysis.get('summary', {})
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Procedures", summary.get('total_procedures', 0))
        
        with col2:
            st.metric("Unique Tables", summary.get('unique_tables', 0))
        
        with col3:
            st.metric("Total Joins", summary.get('total_joins', 0))
        
        with col4:
            st.metric("Total Filters", summary.get('total_filters', 0))
    
    def _render_results_tab(self):
        """Render the analysis results tab"""
        
        if not st.session_state.analysis_results:
            st.info("📊 No analysis results yet. Please upload and analyze procedures first.")
            return
        
        st.header("📊 Analysis Results")
        
        results = st.session_state.analysis_results
        procedures = results['procedures']
        cross_analysis = results['cross_analysis']
        
        # Summary metrics
        st.subheader("📈 Summary Metrics")
        self._show_detailed_metrics(cross_analysis)
        
        # Table-by-table analysis (your requested format)
        st.subheader("📋 Table-by-Table Analysis")
        table_df = self._create_table_analysis_dataframe(procedures)
        
        if not table_df.empty:
            st.dataframe(
                table_df,
                use_container_width=True,
                height=400
            )
            
            # Download button for table analysis
            csv_data = table_df.to_csv(index=False)
            st.download_button(
                label="📥 Download Table Analysis (CSV)",
                data=csv_data,
                file_name=f"table_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
        
        # Filter analysis
        st.subheader("🔍 Filter Pattern Analysis")
        self._show_filter_analysis(cross_analysis.get('filter_analysis', {}))
        
        # Join analysis
        st.subheader("🔗 Join Pattern Analysis")
        self._show_join_analysis(cross_analysis.get('join_analysis', {}))
    
    def _create_table_analysis_dataframe(self, procedures: List[ProcedureAnalysis]) -> pd.DataFrame:
        """Create table analysis dataframe in the exact format requested"""
        
        rows = []
        
        for proc in procedures:
            for table_name, table_ref in proc.tables.items():
                # Get filters for this table
                table_filters = [f for f in proc.filters if f.table == table_name]
                filter_conditions = " AND ".join([f.condition_text for f in table_filters])
                
                # Get joins for this table
                table_joins = [j for j in proc.joins if j.left_table == table_name or j.right_table == table_name]
                join_conditions = " AND ".join([j.condition for j in table_joins])
                
                # Format columns
                columns_str = ", ".join([f"{table_name}.{col}" for col in sorted(table_ref.columns)])
                
                # Usage contexts
                used_as = ", ".join(sorted(table_ref.usage_contexts))
                
                # Table purpose
                notes = self.sql_parser.sap_table_patterns.get(table_name, "Business data table")
                
                row = {
                    'Procedure ID': proc.procedure_id,
                    'Table Name': table_name,
                    'Column Name': columns_str,
                    'Used As': used_as,
                    'Filter Condition': filter_conditions,
                    'Join Condition': join_conditions,
                    'Notes': notes
                }
                rows.append(row)
        
        return pd.DataFrame(rows)
    
    def _show_detailed_metrics(self, cross_analysis: Dict[str, Any]):
        """Show detailed metrics"""
        
        summary = cross_analysis.get('summary', {})
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.metric("Total Procedures", summary.get('total_procedures', 0))
            st.metric("Unique Tables", summary.get('unique_tables', 0))
        
        with col2:
            st.metric("Total Joins", summary.get('total_joins', 0))
            st.metric("Total Filters", summary.get('total_filters', 0))
    
    def _show_filter_analysis(self, filter_analysis: Dict[str, Any]):
        """Show filter pattern analysis"""
        
        if not filter_analysis:
            st.info("No filter analysis available")
            return
        
        # Recommendations
        recommendations = filter_analysis.get('recommendations', [])
        if recommendations:
            st.warning("⚠️ Migration Considerations:")
            for rec in recommendations:
                st.write(f"- {rec}")
        
        # Filter patterns summary
        summary = filter_analysis.get('summary', {})
        if summary:
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Filtered Columns", summary.get('total_filtered_columns', 0))
            with col2:
                avg_coverage = summary.get('avg_filter_coverage', 0)
                st.metric("Avg Filter Coverage", f"{avg_coverage:.1f}%")
    
    def _show_join_analysis(self, join_analysis: Dict[str, Any]):
        """Show join pattern analysis"""
        
        if not join_analysis:
            st.info("No join analysis available")
            return
        
        # Complexity issues
        complexity_issues = join_analysis.get('complexity_issues', [])
        if complexity_issues:
            st.warning("⚠️ Join Complexity Issues:")
            for issue in complexity_issues:
                st.write(f"- {issue}")
        
        # Join patterns summary
        summary = join_analysis.get('summary', {})
        if summary:
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Join Patterns", summary.get('total_join_patterns', 0))
            with col2:
                avg_procs = summary.get('avg_procedures_per_join', 0)
                st.metric("Avg Procedures per Join", f"{avg_procs:.1f}")
    
    def _render_llm_tab(self):
        """Render the LLM requirements generation tab"""
        
        if not st.session_state.analysis_results:
            st.info("🤖 Please complete the SQL analysis first to generate requirements.")
            return
        
        st.header("🤖 LLM Requirements Generation")
        
        # Provider selection
        provider = st.session_state.get('selected_provider', 'azure')
        st.info(f"Using {provider.title()} LLM provider")
        
        # Custom prompt option
        use_custom_prompt = st.checkbox("Use custom prompt")
        custom_prompt = None
        
        if use_custom_prompt:
            custom_prompt = st.text_area(
                "Custom Prompt",
                height=200,
                placeholder="Enter your custom prompt for requirements generation..."
            )
        
        # Generate requirements
        if st.button("🚀 Generate Requirements", type="primary"):
            self._generate_requirements(provider, custom_prompt)
        
        # Show previous results
        if st.session_state.llm_response:
            self._show_llm_results()
    
    def _generate_requirements(self, provider: str, custom_prompt: Optional[str]):
        """Generate requirements using LLM"""
        
        with st.spinner(f"🤖 Generating requirements using {provider.title()}..."):
            try:
                analysis_data = st.session_state.analysis_results['cross_analysis']
                
                response = self.llm_service.generate_requirements(
                    analysis_data=analysis_data,
                    provider=provider,
                    custom_prompt=custom_prompt
                )
                
                st.session_state.llm_response = response
                
                if response.success:
                    st.success(f"✅ Requirements generated successfully using {response.provider}!")
                    if response.response_time:
                        st.info(f"⏱️ Generation time: {response.response_time:.2f} seconds")
                else:
                    st.error(f"❌ Requirements generation failed: {response.error}")
                
            except Exception as e:
                st.error(f"❌ Error generating requirements: {e}")
                st.exception(e)
    
    def _show_llm_results(self):
        """Show LLM generation results"""
        
        response = st.session_state.llm_response
        
        if not response or not response.success:
            return
        
        st.subheader("📄 Generated Requirements")
        
        # Metadata
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Provider", response.provider.title())
        with col2:
            st.metric("Model", response.model)
        with col3:
            if response.tokens_used:
                st.metric("Tokens Used", response.tokens_used)
        
        # Content
        st.markdown(response.content)
        
        # Download button
        st.download_button(
            label="📥 Download Requirements (TXT)",
            data=response.content,
            file_name=f"requirements_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            mime="text/plain"
        )
    
    def _render_visualizations_tab(self):
        """Render visualizations tab"""
        
        if not st.session_state.analysis_results:
            st.info("📈 Please complete the SQL analysis first to view visualizations.")
            return
        
        st.header("📈 Analysis Visualizations")
        
        results = st.session_state.analysis_results
        procedures = results['procedures']
        cross_analysis = results['cross_analysis']
        
        # Complexity distribution
        st.subheader("📊 Procedure Complexity Distribution")
        self._plot_complexity_distribution(procedures)
        
        # Table usage frequency
        st.subheader("📋 Table Usage Frequency")
        self._plot_table_usage(procedures)
        
        # Data lineage network
        st.subheader("🔗 Data Lineage Network")
        self._plot_data_lineage(cross_analysis.get('data_lineage', {}))
    
    def _plot_complexity_distribution(self, procedures: List[ProcedureAnalysis]):
        """Plot procedure complexity distribution"""
        
        complexity_scores = [p.complexity_score for p in procedures]
        procedure_names = [p.procedure_id for p in procedures]
        
        fig = px.bar(
            x=procedure_names,
            y=complexity_scores,
            title="Procedure Complexity Scores",
            labels={'x': 'Procedure', 'y': 'Complexity Score'}
        )
        fig.update_xaxis(tickangle=45)
        st.plotly_chart(fig, use_container_width=True)
    
    def _plot_table_usage(self, procedures: List[ProcedureAnalysis]):
        """Plot table usage frequency"""
        
        table_counts = {}
        for proc in procedures:
            for table_name in proc.tables.keys():
                table_counts[table_name] = table_counts.get(table_name, 0) + 1
        
        if table_counts:
            # Sort by frequency
            sorted_tables = sorted(table_counts.items(), key=lambda x: x[1], reverse=True)
            tables, counts = zip(*sorted_tables[:20])  # Top 20
            
            fig = px.bar(
                x=list(tables),
                y=list(counts),
                title="Top 20 Most Used Tables",
                labels={'x': 'Table Name', 'y': 'Usage Count'}
            )
            fig.update_xaxis(tickangle=45)
            st.plotly_chart(fig, use_container_width=True)
    
    def _plot_data_lineage(self, lineage_data: Dict[str, Any]):
        """Plot data lineage network"""
        
        if not lineage_data or not lineage_data.get('nodes'):
            st.info("No data lineage information available")
            return
        
        # Create network visualization using plotly
        nodes = lineage_data.get('nodes', [])
        edges = lineage_data.get('edges', [])
        
        if nodes:
            # Simple table relationship chart
            table_names = [node[0] for node in nodes]
            table_types = [node[1].get('table_type', 'Unknown') for node in nodes]
            
            fig = px.treemap(
                names=table_names,
                parents=[''] * len(table_names),
                values=[1] * len(table_names),
                title="Table Relationships Overview"
            )
            st.plotly_chart(fig, use_container_width=True)
    
    def _render_download_buttons(self):
        """Render download buttons in sidebar"""
        
        if not st.session_state.analysis_results:
            return
        
        # Create download package
        if st.sidebar.button("📦 Download All Results"):
            self._create_download_package()
    
    def _create_download_package(self):
        """Create a zip file with all analysis results"""
        
        zip_buffer = io.BytesIO()
        
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            
            # Table analysis CSV
            if st.session_state.analysis_results:
                procedures = st.session_state.analysis_results['procedures']
                table_df = self._create_table_analysis_dataframe(procedures)
                if not table_df.empty:
                    csv_data = table_df.to_csv(index=False)
                    zip_file.writestr("table_analysis.csv", csv_data)
            
            # LLM requirements
            if st.session_state.llm_response and st.session_state.llm_response.success:
                zip_file.writestr("requirements.txt", st.session_state.llm_response.content)
            
            # Analysis summary JSON
            if st.session_state.analysis_results:
                # Convert to JSON-serializable format
                summary_data = {
                    'timestamp': st.session_state.analysis_results['timestamp'],
                    'summary': st.session_state.analysis_results['cross_analysis']['summary']
                }
                zip_file.writestr("analysis_summary.json", json.dumps(summary_data, indent=2))
        
        zip_buffer.seek(0)
        
        st.sidebar.download_button(
            label="📥 Download Package",
            data=zip_buffer.getvalue(),
            file_name=f"sap_hana_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip",
            mime="application/zip"
        )

def main():
    """Main application entry point"""
    app = SAP_HANA_Analyzer_App()
    app.run()

if __name__ == "__main__":
    main()
