#!/usr/bin/env python3
"""
Requirements Documentation Generator for SAP HANA SQL Procedures
================================================================

This script generates comprehensive requirements documentation from analyzed
SQL procedures, formatted for optimal LLM consumption and understanding.

Features:
- Consolidated view of all tables, columns, and relationships
- Data lineage and dependency mapping
- Business logic extraction from SQL procedures
- Structured output for LLM processing
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Set, Any, Optional
from collections import defaultdict, Counter
from dataclasses import asdict
import networkx as nx

class RequirementsDocumentationGenerator:
    """Generate comprehensive requirements documentation"""
    
    def __init__(self, analysis_data: List[Dict], output_dir: str = "analysis_output"):
        self.analysis_data = analysis_data
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Consolidated data structures
        self.all_tables = defaultdict(set)  # table -> columns
        self.table_relationships = []
        self.column_usage = defaultdict(list)  # column -> usage contexts
        self.business_rules = []
        self.data_flow = nx.DiGraph()
    
    def consolidate_metadata(self):
        """Consolidate metadata from all procedures"""
        print("Consolidating metadata from all procedures...")
        
        for analysis in self.analysis_data:
            procedure_name = analysis.get('procedure_name', 'unknown')
            
            # Consolidate tables and columns
            for table in analysis.get('tables', []):
                table_name = table.get('full_name', table.get('name', ''))
                self.all_tables[table_name].update([])  # Initialize if not exists
            
            for column in analysis.get('columns', []):
                col_name = column.get('name', '')
                table_ref = column.get('table', '')
                context = column.get('context', 'unknown')
                
                self.column_usage[col_name].append({
                    'procedure': procedure_name,
                    'table': table_ref,
                    'context': context
                })
                
                # Add to table's column set
                if table_ref:
                    self.all_tables[table_ref].add(col_name)
            
            # Consolidate joins for relationships
            for join in analysis.get('joins', []):
                relationship = {
                    'procedure': procedure_name,
                    'join_type': join.get('join_type', 'INNER'),
                    'left_table': join.get('left_table', ''),
                    'right_table': join.get('right_table', ''),
                    'condition': join.get('condition', '')
                }
                self.table_relationships.append(relationship)
                
                # Add to data flow graph
                left_table = join.get('left_table', '')
                right_table = join.get('right_table', '')
                if left_table and right_table:
                    self.data_flow.add_edge(left_table, right_table, 
                                          join_type=join.get('join_type', 'INNER'))
    
    def generate_table_catalog(self) -> Dict[str, Any]:
        """Generate comprehensive table catalog"""
        catalog = {
            'summary': {
                'total_tables': len(self.all_tables),
                'total_unique_columns': len(set().union(*self.all_tables.values())),
                'most_used_tables': self._get_most_used_tables(),
                'most_used_columns': self._get_most_used_columns()
            },
            'tables': {}
        }
        
        for table_name, columns in self.all_tables.items():
            # Analyze table usage across procedures
            table_usage = self._analyze_table_usage(table_name)
            
            catalog['tables'][table_name] = {
                'columns': sorted(list(columns)),
                'column_count': len(columns),
                'usage_frequency': table_usage['frequency'],
                'used_in_procedures': table_usage['procedures'],
                'join_relationships': self._get_table_relationships(table_name),
                'estimated_purpose': self._estimate_table_purpose(table_name, columns)
            }
        
        return catalog
    
    def generate_data_lineage(self) -> Dict[str, Any]:
        """Generate data lineage and flow documentation"""
        lineage = {
            'summary': {
                'total_relationships': len(self.table_relationships),
                'connected_components': nx.number_connected_components(self.data_flow.to_undirected()),
                'most_connected_tables': self._get_most_connected_tables()
            },
            'relationships': self.table_relationships,
            'data_flow_paths': self._extract_data_flow_paths(),
            'dependency_matrix': self._create_dependency_matrix()
        }
        
        return lineage
    
    def generate_business_logic_summary(self) -> Dict[str, Any]:
        """Extract and summarize business logic from procedures"""
        business_logic = {
            'summary': {
                'total_procedures': len(self.analysis_data),
                'common_patterns': self._identify_common_patterns(),
                'filter_patterns': self._analyze_filter_patterns(),
                'aggregation_patterns': self._analyze_aggregation_patterns()
            },
            'procedures': []
        }
        
        for analysis in self.analysis_data:
            procedure_logic = {
                'name': analysis.get('procedure_name', ''),
                'complexity_score': self._calculate_complexity_score(analysis),
                'main_tables': self._get_main_tables(analysis),
                'key_filters': self._extract_key_filters(analysis),
                'business_purpose': self._infer_business_purpose(analysis)
            }
            business_logic['procedures'].append(procedure_logic)
        
        return business_logic
    
    def generate_llm_optimized_summary(self) -> Dict[str, Any]:
        """Generate LLM-optimized summary for requirements documentation"""
        summary = {
            'system_overview': {
                'description': 'SAP HANA SQL Procedures Analysis for Requirements Documentation',
                'scope': f'Analysis of {len(self.analysis_data)} SQL procedures',
                'key_metrics': {
                    'total_tables': len(self.all_tables),
                    'total_relationships': len(self.table_relationships),
                    'complexity_distribution': self._get_complexity_distribution()
                }
            },
            'data_architecture': {
                'core_tables': self._identify_core_tables(),
                'lookup_tables': self._identify_lookup_tables(),
                'transaction_tables': self._identify_transaction_tables(),
                'integration_points': self._identify_integration_points()
            },
            'business_processes': {
                'identified_processes': self._identify_business_processes(),
                'data_transformation_patterns': self._identify_transformation_patterns(),
                'common_business_rules': self._extract_common_business_rules()
            },
            'technical_requirements': {
                'performance_considerations': self._identify_performance_considerations(),
                'data_quality_requirements': self._identify_data_quality_requirements(),
                'integration_requirements': self._identify_integration_requirements()
            }
        }
        
        return summary
    
    def _get_most_used_tables(self, top_n: int = 10) -> List[Dict[str, Any]]:
        """Get most frequently used tables"""
        table_usage = Counter()
        for analysis in self.analysis_data:
            for table in analysis.get('tables', []):
                table_name = table.get('full_name', table.get('name', ''))
                table_usage[table_name] += 1
        
        return [{'table': table, 'usage_count': count} 
                for table, count in table_usage.most_common(top_n)]
    
    def _get_most_used_columns(self, top_n: int = 20) -> List[Dict[str, Any]]:
        """Get most frequently used columns"""
        column_usage = Counter()
        for column, usages in self.column_usage.items():
            column_usage[column] = len(usages)
        
        return [{'column': column, 'usage_count': count} 
                for column, count in column_usage.most_common(top_n)]
    
    def _analyze_table_usage(self, table_name: str) -> Dict[str, Any]:
        """Analyze how a table is used across procedures"""
        usage_count = 0
        procedures = []
        
        for analysis in self.analysis_data:
            for table in analysis.get('tables', []):
                if table.get('full_name', table.get('name', '')) == table_name:
                    usage_count += 1
                    procedures.append(analysis.get('procedure_name', ''))
                    break
        
        return {
            'frequency': usage_count,
            'procedures': list(set(procedures))
        }
    
    def _get_table_relationships(self, table_name: str) -> List[Dict[str, Any]]:
        """Get all relationships for a specific table"""
        relationships = []
        for rel in self.table_relationships:
            if rel['left_table'] == table_name or rel['right_table'] == table_name:
                relationships.append(rel)
        return relationships
    
    def _estimate_table_purpose(self, table_name: str, columns: Set[str]) -> str:
        """Estimate the business purpose of a table based on naming patterns"""
        name_lower = table_name.lower()
        
        # Common SAP table patterns
        if any(pattern in name_lower for pattern in ['mara', 'material', 'item']):
            return 'Material/Product Master'
        elif any(pattern in name_lower for pattern in ['kna1', 'customer', 'client']):
            return 'Customer Master'
        elif any(pattern in name_lower for pattern in ['lfa1', 'vendor', 'supplier']):
            return 'Vendor Master'
        elif any(pattern in name_lower for pattern in ['vbak', 'sales', 'order']):
            return 'Sales Order'
        elif any(pattern in name_lower for pattern in ['vbap', 'item', 'position']):
            return 'Sales Order Item'
        elif any(pattern in name_lower for pattern in ['bkpf', 'document', 'header']):
            return 'Document Header'
        elif any(pattern in name_lower for pattern in ['bseg', 'line', 'item']):
            return 'Document Line Item'
        else:
            return 'Business Data Table'
    
    def generate_all_documentation(self):
        """Generate all documentation files"""
        print("Generating comprehensive requirements documentation...")
        
        # Consolidate metadata first
        self.consolidate_metadata()
        
        # Generate different documentation sections
        table_catalog = self.generate_table_catalog()
        data_lineage = self.generate_data_lineage()
        business_logic = self.generate_business_logic_summary()
        llm_summary = self.generate_llm_optimized_summary()
        
        # Save all documentation
        self._save_json(table_catalog, 'table_catalog.json')
        self._save_json(data_lineage, 'data_lineage.json')
        self._save_json(business_logic, 'business_logic.json')
        self._save_json(llm_summary, 'llm_optimized_summary.json')
        
        # Generate consolidated report
        consolidated_report = {
            'table_catalog': table_catalog,
            'data_lineage': data_lineage,
            'business_logic': business_logic,
            'llm_summary': llm_summary
        }
        
        self._save_json(consolidated_report, 'consolidated_requirements_report.json')
        
        # Generate human-readable markdown
        self._generate_markdown_report(consolidated_report)
        
        print(f"✅ Documentation generated in: {self.output_dir}")
    
    def _save_json(self, data: Dict[str, Any], filename: str):
        """Save data as JSON file"""
        filepath = self.output_dir / filename
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
    
    # Placeholder methods for complex analysis functions
    def _get_most_connected_tables(self): return []
    def _extract_data_flow_paths(self): return []
    def _create_dependency_matrix(self): return {}
    def _identify_common_patterns(self): return []
    def _analyze_filter_patterns(self): return []
    def _analyze_aggregation_patterns(self): return []
    def _calculate_complexity_score(self, analysis): return 0
    def _get_main_tables(self, analysis): return []
    def _extract_key_filters(self, analysis): return []
    def _infer_business_purpose(self, analysis): return "Data Processing"
    def _get_complexity_distribution(self): return {}
    def _identify_core_tables(self): return []
    def _identify_lookup_tables(self): return []
    def _identify_transaction_tables(self): return []
    def _identify_integration_points(self): return []
    def _identify_business_processes(self): return []
    def _identify_transformation_patterns(self): return []
    def _extract_common_business_rules(self): return []
    def _identify_performance_considerations(self): return []
    def _identify_data_quality_requirements(self): return []
    def _identify_integration_requirements(self): return []
    def _generate_markdown_report(self, report): pass

def main():
    """Main function to generate requirements documentation"""
    # This would typically load analysis data from the analyzer
    print("Requirements Documentation Generator")
    print("Please run sql_analyzer.py first to generate analysis data.")

if __name__ == "__main__":
    main()
