#!/usr/bin/env python3
"""
Standalone LLM Requirements Generator
====================================

This script generates requirements documentation using Azure OpenAI
from previously analyzed SQL procedure data.

Usage:
    python generate_llm_requirements.py

Prerequisites:
    - Run 'python run_complete_analysis.py' first to generate analysis data
    - Ensure Azure OpenAI API access is configured
"""

import sys
from pathlib import Path
from llm_requirements_generator import LLMRequirementsGenerator

def main():
    """Main function to generate LLM-powered requirements"""
    print("🤖 SAP HANA SQL LLM Requirements Generator")
    print("=" * 50)
    
    # Check if analysis data exists
    analysis_dir = Path("analysis_output")
    if not analysis_dir.exists():
        print("❌ Analysis output directory not found.")
        print("\nPlease run the complete analysis first:")
        print("   python run_complete_analysis.py")
        print("\nOr if you have already run the analysis, make sure you're in the correct directory.")
        return False
    
    # Check for required analysis files
    required_files = [
        'table_catalog.json',
        'business_logic.json', 
        'raw_analysis_data.json'
    ]
    
    missing_files = []
    for filename in required_files:
        if not (analysis_dir / filename).exists():
            missing_files.append(filename)
    
    if missing_files:
        print(f"❌ Missing required analysis files: {missing_files}")
        print("\nPlease run the complete analysis first:")
        print("   python run_complete_analysis.py")
        return False
    
    print("✅ Analysis data found")
    
    # Initialize and run LLM generator
    try:
        print("\n🚀 Initializing LLM Requirements Generator...")
        generator = LLMRequirementsGenerator()
        
        print("📊 Analysis data loaded successfully")
        print(f"   - Tables identified: {generator.analysis_data.get('table_catalog', {}).get('summary', {}).get('total_tables', 'Unknown')}")
        print(f"   - Procedures analyzed: {len(generator.analysis_data.get('raw_analysis_data', []))}")
        
        # Generate documentation
        generator.generate_all_documentation()
        
        print("\n✅ LLM-powered requirements documentation generated successfully!")
        print(f"📁 Output location: {generator.output_dir}")
        
        # List generated files
        generated_files = list(generator.output_dir.glob("*.md"))
        if generated_files:
            print("\n📄 Generated files:")
            for file in generated_files:
                print(f"   - {file.name}")
        
        print("\n🎯 Next steps:")
        print("1. Review the generated documentation files")
        print("2. Validate business requirements with stakeholders")
        print("3. Use technical specifications for system design")
        print("4. Follow migration guide for implementation planning")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating LLM requirements: {e}")
        print("\nPossible issues:")
        print("- Check your Azure OpenAI API configuration")
        print("- Verify internet connectivity")
        print("- Ensure the API token is valid")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
