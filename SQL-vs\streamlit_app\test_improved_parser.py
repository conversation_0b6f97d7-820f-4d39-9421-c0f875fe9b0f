#!/usr/bin/env python3
"""
Test Improved Parser
====================

Test the improved parser with BSEG and other SAP tables to ensure:
1. No false table names like "DATE", "PAYMENT", "AND"
2. Proper BSEG filter and join detection
3. Better filter analysis recommendations
"""

def test_improved_parser():
    """Test the improved parser"""
    print("🔍 Testing Improved Parser...")
    
    try:
        from sql_parser import AdvancedSQLParser
        
        parser = AdvancedSQLParser()
        
        # Test SQL with BSEG (based on vendor_payment_analysis.sql)
        bseg_sql = '''
        CREATE PROCEDURE vendor_payment_analysis AS
        BEGIN
            SELECT 
                h.bukrs as company_code,
                h.belnr as document_number,
                i.lifnr as vendor_number,
                i.dmbtr as amount_local_currency,
                CASE 
                    WHEN DAYS_BETWEEN(i.zfbdt, CURRENT_DATE) <= i.zbd1t 
                    THEN 'DISCOUNT_PERIOD'
                    ELSE 'OVERDUE'
                END as payment_status
            FROM BKPF h
            INNER JOIN BSEG i ON h.belnr = i.belnr 
                             AND h.gjahr = i.gjahr 
                             AND h.bukrs = i.bukrs
            INNER JOIN LFA1 v ON i.lifnr = v.lifnr
            WHERE h.bukrs = 'US01'
              AND h.gjahr = '2024'
              AND i.koart = 'K'
              AND i.lifnr IS NOT NULL;
        END;
        '''
        
        # Test SQL with VBRK filters (your example)
        vbrk_sql = '''
        CREATE PROCEDURE billing_analysis AS
        BEGIN
            SELECT VBRK.VBELN, VBRK.FKART, VBRP.POSNR
            FROM VBRK
            INNER JOIN VBRP ON VBRK.VBELN = VBRP.VBELN
            WHERE VBRK.FKART IN ('F2', 'G2', 'S1')
            AND VBRK.FKDAT > '20240101';
        END;
        '''
        
        # Test SQL that uses VBRK without filters
        vbrk_no_filter_sql = '''
        CREATE PROCEDURE billing_report AS
        BEGIN
            SELECT VBRK.VBELN, VBRK.FKART, VBRK.NETWR
            FROM VBRK
            WHERE VBRK.FKDAT > '20240101';
        END;
        '''
        
        print("📝 Parsing procedures...")
        
        # Parse all procedures
        result1 = parser.parse_procedure(bseg_sql, "vendor_payment_analysis")
        result2 = parser.parse_procedure(vbrk_sql, "billing_analysis")
        result3 = parser.parse_procedure(vbrk_no_filter_sql, "billing_report")
        
        procedures = [result1, result2, result3]
        
        print(f"\n📊 Individual Results:")
        for i, result in enumerate(procedures, 1):
            print(f"   Procedure {i} ({result.procedure_id}):")
            print(f"     Tables: {list(result.tables.keys())}")
            print(f"     Filters: {len(result.filters)}")
            for filter_cond in result.filters:
                print(f"       - {filter_cond.table}.{filter_cond.column} {filter_cond.operator} {filter_cond.values}")
        
        # Cross-procedure analysis
        print(f"\n🔄 Cross-Procedure Analysis...")
        cross_analysis = parser.analyze_cross_procedure_patterns(procedures)
        
        # Check table consolidation
        table_consolidation = cross_analysis.get('table_consolidation', {})
        print(f"\n📋 Table Consolidation (One Row Per Table):")
        for table_name, table_data in table_consolidation.items():
            print(f"   Table: {table_name}")
            print(f"     Used in procedures: {table_data['used_in_procedures']}")
            print(f"     All columns: {table_data['all_columns']}")
            print(f"     Migration priority: {table_data['migration_priority']}")
            
            # Check filter consolidation
            filter_info = table_data['consolidated_filters']
            if filter_info['has_filters']:
                print(f"     Filter patterns:")
                for pattern in filter_info['patterns']:
                    print(f"       - {pattern['column']}: {pattern['unique_values']} ({pattern['migration_note']})")
        
        # Check improved filter analysis
        filter_analysis = cross_analysis.get('filter_analysis', {})
        recommendations = filter_analysis.get('recommendations', [])
        print(f"\n⚠️ Improved Migration Recommendations:")
        for rec in recommendations:
            print(f"   {rec}")
        
        # Verify no false table names
        all_tables = set()
        for table_name in table_consolidation.keys():
            all_tables.add(table_name)
        
        false_positives = {'DATE', 'PAYMENT', 'AND', 'OR', 'CASE', 'WHEN', 'THEN', 'ELSE'}
        found_false_positives = all_tables.intersection(false_positives)
        
        if found_false_positives:
            print(f"\n❌ Found false positive table names: {found_false_positives}")
        else:
            print(f"\n✅ No false positive table names found!")
        
        print(f"\n✅ All tables found: {sorted(all_tables)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run improved parser test"""
    print("🧪 Improved Parser Test")
    print("=" * 30)
    
    if test_improved_parser():
        print("\n✅ Test completed successfully!")
        print("🚀 Ready for Streamlit app with improved parsing")
    else:
        print("\n❌ Test failed!")

if __name__ == "__main__":
    main()
