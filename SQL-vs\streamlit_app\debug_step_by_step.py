#!/usr/bin/env python3
"""
Debug Step by Step
==================

Debug the column extraction method step by step.
"""

import re

sql = '''
SELECT 
    h.bukrs as company_code,
    i.lifnr as vendor_number
FROM bkpf h
INNER JOIN bseg i ON h.belnr = i.belnr 
'''

def debug_extract_columns_for_table(sql_text: str, table_name: str):
    """Debug version of the fixed method"""
    print(f"\n🔍 Debugging column extraction for {table_name}")
    print(f"SQL: {sql_text}")
    
    columns = set()
    
    # Step 1: Build alias mapping
    print(f"\n1. Building alias mapping...")
    alias_to_table = {}
    alias_patterns = [
        r'FROM\s+([a-zA-Z][a-zA-Z0-9_]{2,7})\s+(?:AS\s+)?(\w+)',
        r'JOIN\s+([a-zA-Z][a-zA-Z0-9_]{2,7})\s+(?:AS\s+)?(\w+)',
        r'INNER\s+JOIN\s+([a-zA-Z][a-zA-Z0-9_]{2,7})\s+(?:AS\s+)?(\w+)',
    ]
    
    for pattern in alias_patterns:
        matches = re.finditer(pattern, sql_text, re.IGNORECASE)
        for match in matches:
            table = match.group(1).upper()
            alias = match.group(2).upper()
            if alias != table and len(alias) <= 3:
                alias_to_table[alias] = table
    
    print(f"   Alias mapping: {alias_to_table}")
    
    # Step 2: Find all table.column patterns
    print(f"\n2. Finding all table.column patterns...")
    column_pattern = r'(\w+)\.(\w+)'
    matches = re.findall(column_pattern, sql_text, re.IGNORECASE)
    print(f"   All matches: {matches}")
    
    # Step 3: Filter for the target table
    print(f"\n3. Filtering for table {table_name}...")
    for table_or_alias, column in matches:
        table_or_alias_upper = table_or_alias.upper()
        column_upper = column.upper()
        
        print(f"   Checking: {table_or_alias}.{column} -> {table_or_alias_upper}.{column_upper}")
        
        # Check if this is the table we want (direct match)
        if table_or_alias_upper == table_name:
            print(f"     ✅ Direct match for {table_name}")
            columns.add(column_upper)
        # Check if this is an alias for the table we want
        elif table_or_alias_upper in alias_to_table and alias_to_table[table_or_alias_upper] == table_name:
            print(f"     ✅ Alias match: {table_or_alias_upper} -> {alias_to_table[table_or_alias_upper]}")
            columns.add(column_upper)
        else:
            print(f"     ❌ No match for {table_name}")
    
    print(f"\n4. Final columns for {table_name}: {sorted(list(columns))}")
    return columns

# Test the debug version
print("🐛 Step-by-Step Debug")
print("=" * 30)

bseg_columns = debug_extract_columns_for_table(sql, 'BSEG')
bkpf_columns = debug_extract_columns_for_table(sql, 'BKPF')

print(f"\n📊 Final Results:")
print(f"   BSEG: {sorted(list(bseg_columns))}")
print(f"   BKPF: {sorted(list(bkpf_columns))}")

# Expected:
# BSEG should have: ['BELNR', 'LIFNR']
# BKPF should have: ['BELNR', 'BUKRS']
