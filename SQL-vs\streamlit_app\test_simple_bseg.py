#!/usr/bin/env python3
"""
Test Simple BSEG
=================

Simple test for BSEG column extraction.
"""

def test_simple():
    """Simple test"""
    print("🔍 Simple BSEG Test...")
    
    try:
        from sql_parser import AdvancedSQLParser
        print("✅ Parser imported")
        
        parser = AdvancedSQLParser()
        print("✅ Parser created")
        
        # Simple BSEG SQL
        sql = '''
        SELECT MANDT, BUKRS, BELNR FROM BSEG WHERE BUKRS = 'US01';
        '''
        
        result = parser.parse_procedure(sql, "test_bseg")
        print(f"✅ Parsed: {len(result.tables)} tables")
        
        if 'BSEG' in result.tables:
            columns = list(result.tables['BSEG'].columns)
            print(f"✅ BSEG columns: {columns}")
        else:
            print("❌ BSEG not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_simple()
