#!/usr/bin/env python3
"""
Test Streamlit Ready
====================

Test if the Streamlit app is ready with BSEG parsing.
"""

def test_streamlit_ready():
    """Test if Streamlit is ready"""
    print("🔍 Testing Streamlit Readiness...")
    
    try:
        # Test basic imports
        print("1. Testing imports...")
        import streamlit as st
        print("   ✅ Streamlit imported")
        
        import app
        print("   ✅ App imported")
        
        from sql_parser import AdvancedSQLParser
        print("   ✅ SQL Parser imported")
        
        # Test app initialization
        print("\n2. Testing app initialization...")
        analyzer_app = app.SAP_HANA_Analyzer_App()
        print("   ✅ App initialized")
        
        # Test LLM table analysis method
        print("\n3. Testing LLM table analysis...")
        mock_cross_analysis = {
            'table_consolidation': {
                'BSEG': {
                    'used_in_procedures': ['PR_BSEG_ACTIVITY_LOAD'],
                    'all_columns': ['MANDT', 'BUKRS', 'BELNR', 'GJAHR', 'BUZEI', 'BUDAT', 'BLART', 'TCODE', 'SHKZG', 'WRBTR'],
                    'consolidated_filters': {
                        'has_filters': True,
                        'patterns': [
                            {
                                'column': 'BUKRS',
                                'unique_values': ['US01'],
                                'example_conditions': ['BUKRS = :IP_BUKRS']
                            },
                            {
                                'column': 'BUDAT',
                                'unique_values': ['BETWEEN'],
                                'example_conditions': ['BUDAT BETWEEN :IP_BUDAT_FROM AND :IP_BUDAT_TO']
                            }
                        ]
                    },
                    'consolidated_joins': {
                        'has_joins': False,
                        'relationships': []
                    },
                    'migration_priority': 'HIGH',
                    'table_purpose': 'Accounting Document Line Items',
                    'procedure_count': 1
                }
            },
            'filter_analysis': {},
            'join_analysis': {}
        }
        
        prompt = analyzer_app._create_table_analysis_prompt(
            mock_cross_analysis['table_consolidation'],
            mock_cross_analysis['filter_analysis'],
            mock_cross_analysis['join_analysis']
        )
        
        print("   ✅ LLM prompt created")
        print(f"   Prompt contains BSEG: {'BSEG' in prompt}")
        print(f"   Prompt contains columns: {'MANDT' in prompt and 'BUKRS' in prompt}")
        
        # Show what the LLM will receive
        print(f"\n4. LLM Input Preview:")
        print(f"   BSEG table data:")
        bseg_data = mock_cross_analysis['table_consolidation']['BSEG']
        print(f"     - Procedures: {bseg_data['used_in_procedures']}")
        print(f"     - Columns: {bseg_data['all_columns']}")
        print(f"     - Migration Priority: {bseg_data['migration_priority']}")
        print(f"     - Table Purpose: {bseg_data['table_purpose']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run Streamlit readiness test"""
    print("🧪 Streamlit Readiness Test")
    print("=" * 30)
    
    if test_streamlit_ready():
        print("\n✅ Streamlit app is ready!")
        print("\n🚀 Next Steps:")
        print("1. Start Streamlit: streamlit run app.py")
        print("2. Upload your BSEG procedure file")
        print("3. Go to 'Analysis Results' tab")
        print("4. Click '🤖 Generate LLM Table Analysis'")
        print("5. The LLM will receive BSEG data and generate analysis")
        
        print("\n📋 Expected BSEG Output:")
        print("   - Table Name: BSEG")
        print("   - Columns: MANDT, BUKRS, BELNR, GJAHR, BUZEI, BUDAT, BLART, TCODE, SHKZG, WRBTR")
        print("   - Filters: BUKRS parameter filter, BUDAT date range filter")
        print("   - Migration Priority: HIGH")
        print("   - Purpose: Accounting Document Line Items")
    else:
        print("\n❌ Streamlit app not ready!")

if __name__ == "__main__":
    main()
