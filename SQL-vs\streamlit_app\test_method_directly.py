#!/usr/bin/env python3
"""
Test Method Directly
====================

Test the fixed column extraction method directly.
"""

from sql_parser import AdvancedSQLParser

sql = '''
SELECT 
    h.bukrs as company_code,
    i.lifnr as vendor_number
FROM bkpf h
INNER JOIN bseg i ON h.belnr = i.belnr 
'''

parser = AdvancedSQLParser()

print("🔍 Testing Fixed Method Directly")
print("=" * 35)

# Test the fixed method directly
print("1. Testing BSEG column extraction:")
bseg_columns = parser._extract_columns_for_table(sql, 'BSEG')
print(f"   BSEG columns: {sorted(list(bseg_columns))}")

print("\n2. Testing BKPF column extraction:")
bkpf_columns = parser._extract_columns_for_table(sql, 'BKPF')
print(f"   BKPF columns: {sorted(list(bkpf_columns))}")

# Test alias mapping
print("\n3. Testing alias mapping:")
aliases = parser._build_alias_mapping(sql)
print(f"   Aliases: {aliases}")

# Expected:
# BSEG should have: ['BELNR', 'LIFNR']
# BKPF should have: ['BELNR', 'BUKRS']
