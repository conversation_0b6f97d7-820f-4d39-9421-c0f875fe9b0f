#!/usr/bin/env python3
"""
Debug BSEG Parsing
==================

Specifically debug why BSEG table columns like BLART are not being extracted properly.
"""

def debug_bseg_parsing():
    """Debug BSEG table parsing issues"""
    print("🔍 Debugging BSEG Table Parsing...")
    
    try:
        from sql_parser import AdvancedSQLParser
        
        parser = AdvancedSQLParser()
        
        # Real BSEG SQL from vendor_payment_analysis.sql (actual content)
        bseg_sql = '''
        CREATE PROCEDURE vendor_payment_analysis(
            IN p_company_code NVARCHAR(4),
            IN p_fiscal_year NVARCHAR(4)
        )
        LANGUAGE SQLSCRIPT
        SQL SECURITY INVOKER
        READS SQL DATA AS
        BEGIN
            SELECT
                h.bukrs as company_code,
                h.belnr as document_number,
                h.gjahr as fiscal_year,
                h.bldat as document_date,
                h.budat as posting_date,
                i.lifnr as vendor_number,
                v.name1 as vendor_name,
                i.dmbtr as amount_local_currency,
                i.wrbtr as amount_document_currency,
                i.waers as currency,
                i.zfbdt as baseline_date,
                i.zbd1t as cash_discount_days,
                i.zbd2t as payment_terms_days,
                CASE
                    WHEN DAYS_BETWEEN(i.zfbdt, CURRENT_DATE) <= i.zbd1t
                    THEN 'DISCOUNT_PERIOD'
                    WHEN DAYS_BETWEEN(i.zfbdt, CURRENT_DATE) <= i.zbd2t
                    THEN 'PAYMENT_PERIOD'
                    ELSE 'OVERDUE'
                END as payment_status
            FROM bkpf h
            INNER JOIN bseg i ON h.belnr = i.belnr
                             AND h.gjahr = i.gjahr
                             AND h.bukrs = i.bukrs
            INNER JOIN lfa1 v ON i.lifnr = v.lifnr
            WHERE h.bukrs = p_company_code
              AND h.gjahr = p_fiscal_year
              AND i.koart = 'K'
              AND i.lifnr IS NOT NULL
            ORDER BY h.budat DESC, i.lifnr;
        END;
        '''
        
        print("📝 Parsing BSEG procedure...")
        result = parser.parse_procedure(bseg_sql, "vendor_payment_analysis")
        
        print(f"\n📊 Parsing Results:")
        print(f"   Tables found: {list(result.tables.keys())}")
        
        # Check each table's columns
        for table_name, table_ref in result.tables.items():
            print(f"\n🔹 Table: {table_name}")
            print(f"   Columns: {sorted(list(table_ref.columns))}")
            print(f"   Usage contexts: {table_ref.usage_contexts}")
        
        # Check filters
        print(f"\n🔍 Filters found: {len(result.filters)}")
        for i, filter_cond in enumerate(result.filters):
            print(f"   Filter {i+1}: {filter_cond.table}.{filter_cond.column} {filter_cond.operator} {filter_cond.values}")
        
        # Debug alias mapping
        print(f"\n🔧 Debug Alias Mapping:")
        alias_mapping = parser._build_alias_mapping(bseg_sql)
        print(f"   Alias mapping: {alias_mapping}")
        
        # Debug column extraction for BSEG specifically
        print(f"\n🔧 Debug BSEG Column Extraction:")
        bseg_columns = parser._extract_columns_for_table(bseg_sql, 'BSEG')
        print(f"   BSEG columns found: {sorted(list(bseg_columns))}")
        
        # Debug column extraction for BKPF specifically
        print(f"\n🔧 Debug BKPF Column Extraction:")
        bkpf_columns = parser._extract_columns_for_table(bseg_sql, 'BKPF')
        print(f"   BKPF columns found: {sorted(list(bkpf_columns))}")
        
        # Manual check - what columns are actually in the SQL?
        print(f"\n🔧 Manual Column Check:")
        import re
        
        # Find all table.column patterns
        column_pattern = r'(\w+)\.(\w+)'
        matches = re.findall(column_pattern, bseg_sql, re.IGNORECASE)
        
        print(f"   All table.column patterns found:")
        for table, column in matches:
            print(f"     {table.upper()}.{column.upper()}")
        
        # Group by table
        from collections import defaultdict
        manual_columns = defaultdict(set)
        for table, column in matches:
            manual_columns[table.upper()].add(column.upper())
        
        print(f"\n   Manual grouping by table:")
        for table, columns in manual_columns.items():
            print(f"     {table}: {sorted(list(columns))}")
        
        # Check if BLART is in the SQL
        if 'BLART' in bseg_sql.upper():
            print(f"\n✅ BLART found in SQL text")
        else:
            print(f"\n❌ BLART NOT found in SQL text")
        
        # Check specific patterns
        blart_patterns = re.findall(r'(\w+)\.blart', bseg_sql, re.IGNORECASE)
        print(f"   BLART patterns: {blart_patterns}")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run BSEG debug"""
    print("🐛 BSEG Parsing Debug")
    print("=" * 30)
    
    if debug_bseg_parsing():
        print("\n✅ Debug completed!")
    else:
        print("\n❌ Debug failed!")

if __name__ == "__main__":
    main()
