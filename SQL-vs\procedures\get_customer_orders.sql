
CREATE PROCEDURE get_customer_orders(
    IN p_customer_id NVARCHAR(10),
    IN p_date_from DATE,
    IN p_date_to DATE
)
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
READS SQL DATA AS
BEGIN
    SELECT 
        o.client,
        o.vbeln as order_number,
        o.audat as order_date,
        o.kunnr as customer_id,
        c.name1 as customer_name,
        i.posnr as item_number,
        i.matnr as material_number,
        m.maktx as material_description,
        i.kwmeng as quantity,
        i.netwr as net_value
    FROM vbak o
    INNER JOIN vbap i ON o.vbeln = i.vbeln
    INNER JOIN kna1 c ON o.kunnr = c.kunnr
    LEFT JOIN makt m ON i.matnr = m.matnr
    WHERE o.kunnr = p_customer_id
      AND o.audat BETWEEN p_date_from AND p_date_to
      AND o.vbtyp = 'C'
    ORDER BY o.audat DESC, o.vbeln, i.posnr;
END;
