#!/usr/bin/env python3
"""
SAP HANA SQL Parser Setup and Analysis Tool
============================================

This tool sets up ANTLR4 for parsing SAP HANA SQL procedures and extracting
metadata for requirements documentation generation.

Features:
- Parse SAP HANA SQL procedures using ANTLR4
- Extract tables, columns, joins, and filters
- Generate structured metadata for LLM consumption
- Support for batch processing of multiple procedures

Requirements:
- Python 3.7+
- ANTLR4 Python runtime
- Java (for ANTLR4 grammar compilation)
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Set, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict

@dataclass
class TableInfo:
    """Information about a table used in SQL procedures"""
    name: str
    schema: Optional[str] = None
    alias: Optional[str] = None
    columns: Set[str] = None
    
    def __post_init__(self):
        if self.columns is None:
            self.columns = set()

@dataclass
class JoinInfo:
    """Information about joins in SQL procedures"""
    join_type: str  # INNER, LEFT, RIGHT, FULL, CROSS
    left_table: str
    right_table: str
    join_condition: str
    
@dataclass
class FilterInfo:
    """Information about WHERE clause filters"""
    column: str
    operator: str
    value: str
    table: Optional[str] = None

@dataclass
class ProcedureMetadata:
    """Metadata extracted from a SQL procedure"""
    procedure_name: str
    tables: List[TableInfo]
    joins: List[JoinInfo]
    filters: List[FilterInfo]
    input_parameters: List[Dict[str, str]]
    output_parameters: List[Dict[str, str]]
    description: Optional[str] = None

class SAP_HANA_Parser_Setup:
    """Setup and manage ANTLR4 parser for SAP HANA SQL"""
    
    def __init__(self, workspace_dir: str = "."):
        self.workspace_dir = Path(workspace_dir).resolve()
        self.antlr_dir = self.workspace_dir / "antlr-saphana"
        self.generated_dir = self.workspace_dir / "generated"
        self.procedures_dir = self.workspace_dir / "procedures"
        self.output_dir = self.workspace_dir / "analysis_output"

        # Create necessary directories
        for dir_path in [self.generated_dir, self.procedures_dir, self.output_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are installed"""
        print("Checking prerequisites...")
        
        # Check Java
        try:
            result = subprocess.run(['java', '-version'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Java not found. Please install Java 8 or higher.")
                return False
            print("✅ Java found")
        except FileNotFoundError:
            print("❌ Java not found. Please install Java 8 or higher.")
            return False
        
        # Check if ANTLR4 grammar exists
        grammar_file = self.antlr_dir / "Hana.g4"
        if not grammar_file.exists():
            print(f"❌ ANTLR grammar file not found: {grammar_file}")
            return False
        print("✅ ANTLR grammar file found")
        
        return True
    
    def install_antlr_runtime(self):
        """Install ANTLR4 Python runtime"""
        print("Installing ANTLR4 Python runtime...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'antlr4-python3-runtime'], 
                          check=True)
            print("✅ ANTLR4 Python runtime installed")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install ANTLR4 runtime: {e}")
            raise
    
    def download_antlr_jar(self):
        """Download ANTLR4 JAR file"""
        antlr_jar = self.workspace_dir / "antlr-4.13.1-complete.jar"
        
        if antlr_jar.exists():
            print("✅ ANTLR JAR already exists")
            return str(antlr_jar)
        
        print("Downloading ANTLR4 JAR...")
        import urllib.request
        
        url = "https://www.antlr.org/download/antlr-4.13.1-complete.jar"
        try:
            urllib.request.urlretrieve(url, antlr_jar)
            print("✅ ANTLR JAR downloaded")
            return str(antlr_jar)
        except Exception as e:
            print(f"❌ Failed to download ANTLR JAR: {e}")
            raise
    
    def compile_grammar(self):
        """Compile ANTLR4 grammar to Python"""
        print("Compiling ANTLR4 grammar...")
        
        antlr_jar = self.download_antlr_jar()
        grammar_file = self.antlr_dir / "Hana.g4"
        
        # Compile grammar
        cmd = [
            'java', '-jar', antlr_jar,
            '-Dlanguage=Python3',
            '-o', str(self.generated_dir),
            '-visitor',
            '-listener',
            str(grammar_file)
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("✅ Grammar compiled successfully")
            
            # List generated files
            generated_files = list(self.generated_dir.glob("*.py"))
            print(f"Generated files: {[f.name for f in generated_files]}")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Grammar compilation failed: {e}")
            print(f"STDOUT: {e.stdout}")
            print(f"STDERR: {e.stderr}")
            raise
    
    def setup_parser_environment(self):
        """Complete setup of the parser environment"""
        print("Setting up SAP HANA SQL Parser Environment...")
        print("=" * 50)
        
        if not self.check_prerequisites():
            return False
        
        self.install_antlr_runtime()
        self.compile_grammar()
        
        print("\n✅ Parser environment setup complete!")
        print(f"Generated files are in: {self.generated_dir}")
        print(f"Place your SQL procedures in: {self.procedures_dir}")
        print(f"Analysis output will be in: {self.output_dir}")
        
        return True

def main():
    """Main function to setup the parser"""
    parser_setup = SAP_HANA_Parser_Setup()
    
    if parser_setup.setup_parser_environment():
        print("\n🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Place your 50 SQL procedure files in the 'procedures' directory")
        print("2. Run the analysis script to extract metadata")
        print("3. Generate requirements documentation")
    else:
        print("\n❌ Setup failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
