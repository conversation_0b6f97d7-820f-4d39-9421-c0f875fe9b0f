#!/usr/bin/env python3
"""
Test ANTLR Fix
==============

Test the fixed ANTLR integration to ensure it works properly.
"""

def test_antlr_parsing():
    """Test ANTLR parsing with the fix"""
    print("🔍 Testing ANTLR parsing fix...")
    
    try:
        from sql_parser import AdvancedSQLParser
        
        parser = AdvancedSQLParser()
        
        # Simple test SQL
        test_sql = '''
        PROCEDURE "TEST_PROC" AS
        BEGIN
        SELECT VBAK.VBELN, VBAK.AUART
        FROM VBAK
        WHERE VBAK.AUART IN ('OR', 'TA', 'ZOR');
        END;
        '''
        
        print("📝 Parsing test SQL...")
        result = parser.parse_procedure(test_sql, "TEST_PROC")
        
        print(f"✅ Parsed successfully!")
        print(f"   Tables: {list(result.tables.keys())}")
        print(f"   Filters: {len(result.filters)}")
        
        # Check filter values
        for filter_cond in result.filters:
            print(f"   Filter: {filter_cond.table}.{filter_cond.column} {filter_cond.operator} {filter_cond.values}")
        
        return True
        
    except Exception as e:
        print(f"❌ ANTLR test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run ANTLR test"""
    print("🧪 ANTLR Fix Test")
    print("=" * 20)
    
    if test_antlr_parsing():
        print("\n✅ ANTLR fix successful!")
        print("🚀 Ready to run Streamlit app")
    else:
        print("\n❌ ANTLR fix failed!")

if __name__ == "__main__":
    main()
