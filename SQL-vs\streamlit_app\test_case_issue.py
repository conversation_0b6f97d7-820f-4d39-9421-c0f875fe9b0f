#!/usr/bin/env python3
"""
Test Case Issue
===============

Test if the issue is case sensitivity in alias matching.
"""

import re

sql = '''
SELECT 
    h.bukrs as company_code,
    i.lifnr as vendor_number
FROM bkpf h
INNER JOIN bseg i ON h.belnr = i.belnr 
'''

# Test case sensitivity
print("🔍 Testing Case Sensitivity")
print("=" * 30)

# Find all table.column patterns
column_pattern = r'(\w+)\.(\w+)'
matches = re.findall(column_pattern, sql, re.IGNORECASE)
print(f"All matches: {matches}")

# Test alias mapping
alias_to_table = {'H': 'BKPF', 'I': 'BSEG'}
print(f"Alias mapping: {alias_to_table}")

# Test matching logic
for table_or_alias, column in matches:
    table_or_alias_upper = table_or_alias.upper()
    column_upper = column.upper()
    
    print(f"\nChecking: {table_or_alias}.{column}")
    print(f"  Uppercase: {table_or_alias_upper}.{column_upper}")
    
    # Check for BSEG
    if table_or_alias_upper == 'BSEG':
        print(f"  ✅ Direct match for BSEG")
    elif table_or_alias_upper in alias_to_table and alias_to_table[table_or_alias_upper] == 'BSEG':
        print(f"  ✅ Alias match for BSEG: {table_or_alias_upper} -> {alias_to_table[table_or_alias_upper]}")
    else:
        print(f"  ❌ No match for BSEG")
    
    # Check for BKPF
    if table_or_alias_upper == 'BKPF':
        print(f"  ✅ Direct match for BKPF")
    elif table_or_alias_upper in alias_to_table and alias_to_table[table_or_alias_upper] == 'BKPF':
        print(f"  ✅ Alias match for BKPF: {table_or_alias_upper} -> {alias_to_table[table_or_alias_upper]}")
    else:
        print(f"  ❌ No match for BKPF")

# Expected results:
# h.bukrs -> H -> BKPF (should find BUKRS for BKPF)
# i.lifnr -> I -> BSEG (should find LIFNR for BSEG)
