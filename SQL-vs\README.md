# SAP HANA SQL Procedure Analyzer

A comprehensive tool for analyzing SAP HANA SQL procedures to extract metadata and generate requirements documentation optimized for LLM consumption.

## 🎯 Purpose

This tool helps you analyze 50+ SAP HANA SQL procedures to:
- Extract tables, columns, joins, and filters
- Understand data relationships and dependencies
- Generate comprehensive requirements documentation
- Create LLM-optimized summaries for better AI understanding

## 🏗️ Architecture

The solution consists of several components:

1. **ANTLR4 Parser Setup** (`sql_parser_setup.py`) - Sets up the SAP HANA SQL grammar parser
2. **SQL Analyzer** (`sql_analyzer.py`) - Parses SQL procedures and extracts metadata
3. **Requirements Generator** (`requirements_generator.py`) - Creates comprehensive documentation
4. **Complete Workflow** (`run_complete_analysis.py`) - Orchestrates the entire process

## 🚀 Quick Start

### Prerequisites

- Python 3.7 or higher
- Java 8 or higher (for ANTLR4)
- Internet connection (for downloading ANTLR4 JAR)

### Installation & Setup

1. **Clone or download this repository**
2. **Run the complete analysis workflow:**

```bash
python run_complete_analysis.py
```

This single command will:
- Install required Python packages
- Download and setup ANTLR4
- Compile the SAP HANA grammar
- Create sample SQL procedures (if none exist)
- Analyze all procedures
- Generate comprehensive documentation

### Adding Your SQL Procedures

1. Place your SQL procedure files in the `procedures/` directory
2. Supported file extensions: `.sql`, `.SQL`
3. The tool supports standard SAP HANA SQL procedure syntax

## 📁 Directory Structure

```
SQL-vs/
├── antlr-saphana/          # ANTLR grammar files
│   └── Hana.g4            # SAP HANA SQL grammar
├── procedures/             # Your SQL procedure files
│   ├── procedure1.sql
│   ├── procedure2.sql
│   └── ...
├── generated/              # Generated ANTLR parser files
├── analysis_output/        # Generated documentation
│   ├── raw_analysis_data.json
│   ├── table_catalog.json
│   ├── data_lineage.json
│   ├── business_logic.json
│   ├── llm_optimized_summary.json
│   └── consolidated_requirements_report.json
└── *.py                   # Analysis scripts
```

## 📊 Generated Documentation

The tool generates several types of documentation:

### 1. Table Catalog (`table_catalog.json`)
- Complete inventory of all tables and columns
- Usage frequency across procedures
- Estimated business purpose of each table
- Column relationships and dependencies

### 2. Data Lineage (`data_lineage.json`)
- Table relationships and join patterns
- Data flow paths between tables
- Dependency matrix showing table connections

### 3. Business Logic Summary (`business_logic.json`)
- Extracted business rules from SQL procedures
- Common patterns and filters
- Complexity analysis of each procedure

### 4. LLM-Optimized Summary (`llm_optimized_summary.json`)
- Structured summary optimized for LLM consumption
- System overview and architecture
- Business processes and technical requirements
- Integration points and performance considerations

### 5. Consolidated Report (`consolidated_requirements_report.json`)
- Complete analysis in a single file
- All metadata and documentation combined
- Ready for LLM processing

## 🔧 Advanced Usage

### Running Individual Components

```bash
# Setup parser environment only
python sql_parser_setup.py

# Analyze procedures only (after setup)
python sql_analyzer.py

# Generate documentation only (after analysis)
python requirements_generator.py
```

### Customizing Analysis

You can modify the analysis by editing:
- `sql_analyzer.py` - Add custom metadata extraction
- `requirements_generator.py` - Customize documentation format
- `run_complete_analysis.py` - Modify workflow steps

## 📋 Sample Output

The tool identifies and extracts:

**Tables:**
- `VBAK` (Sales Order Header)
- `VBAP` (Sales Order Items)
- `KNA1` (Customer Master)
- `MARA` (Material Master)
- `MAKT` (Material Descriptions)

**Relationships:**
- `VBAK.VBELN = VBAP.VBELN` (Order Header to Items)
- `VBAK.KUNNR = KNA1.KUNNR` (Order to Customer)
- `VBAP.MATNR = MARA.MATNR` (Item to Material)

**Business Logic:**
- Customer order retrieval with date filtering
- Material stock calculations by plant
- Vendor payment analysis with aging

## 🤖 LLM Integration

The generated documentation is optimized for LLM consumption:

1. **Structured JSON format** - Easy for AI to parse and understand
2. **Business context** - Tables and columns include business purpose
3. **Relationship mapping** - Clear data dependencies and flows
4. **Pattern recognition** - Common SQL patterns and business rules identified
5. **Comprehensive metadata** - All necessary information for requirements generation

### Using with LLMs

Feed the `llm_optimized_summary.json` or `consolidated_requirements_report.json` to your LLM with prompts like:

```
"Based on this SAP HANA SQL analysis, generate comprehensive requirements 
documentation for a system that processes customer orders, manages inventory, 
and handles vendor payments. Include functional requirements, data requirements, 
and integration specifications."
```

## 🛠️ Troubleshooting

### Common Issues

1. **Java not found**
   - Install Java 8 or higher
   - Ensure `java` is in your PATH

2. **ANTLR compilation fails**
   - Check internet connection
   - Verify Java installation
   - Try running setup again

3. **No SQL files found**
   - Place `.sql` files in the `procedures/` directory
   - Check file extensions (`.sql` or `.SQL`)

4. **Parsing errors**
   - Verify SQL syntax is valid SAP HANA SQL
   - Check for unsupported SQL constructs
   - Review error messages in console output

### Getting Help

1. Check the console output for detailed error messages
2. Verify your SQL procedures follow SAP HANA syntax
3. Ensure all prerequisites are installed correctly

## 🔄 Workflow Summary

1. **Setup** - Install dependencies and compile grammar
2. **Parse** - Analyze SQL procedures using ANTLR4
3. **Extract** - Pull out tables, columns, joins, filters
4. **Consolidate** - Combine metadata from all procedures
5. **Generate** - Create comprehensive documentation
6. **Optimize** - Format for LLM consumption

## 📈 Benefits

- **Automated Analysis** - No manual SQL review required
- **Comprehensive Coverage** - Analyzes all 50+ procedures together
- **LLM-Ready Output** - Optimized for AI consumption
- **Business Context** - Includes business purpose and relationships
- **Scalable** - Can handle large numbers of procedures
- **Extensible** - Easy to customize and extend

## 🎯 Use Cases

- **Requirements Documentation** - Generate comprehensive system requirements
- **System Migration** - Understand existing system before migration
- **Code Review** - Analyze SQL procedures for patterns and issues
- **Data Governance** - Understand data usage and dependencies
- **Architecture Documentation** - Document data architecture and flows

---

**Ready to analyze your SAP HANA SQL procedures?**

Run: `python run_complete_analysis.py`
