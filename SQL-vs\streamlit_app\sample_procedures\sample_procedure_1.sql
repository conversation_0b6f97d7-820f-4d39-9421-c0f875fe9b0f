PROCEDURE "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::PR_GS_O2C_ADDRC_DELTA" ( IN IP_ERDAT NVARCHAR(8) )
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
DEFAULT SCHEMA GSAP_ECC
AS
BEGIN
/****************************
CR/Project : 580824/ Celonis_OTC_DELTA
Purpose    : Filling activity table with VBAK, VBAP, VBFA, VBRP, VBRK, CDPOS, CDHDR, BSEG, BKPF, USH02 and USR02 tables
Activity   : Acc doc dispute reason code
*****************************/
DECLARE DATE_VALUE NVARCHAR(10);
DECLARE TIME_VALUE NVARCHAR(10);
DECLARE V_PRTN_ID NVARCHAR(10);

-- Exit handler to catch error message, if any.
DECLARE EXIT HANDLER FOR SQLEXCEPTION	
BEGIN   
    -- Display error message, if any.
	SELECT (::SQL_ERROR_CODE || ' :' || ::SQL_ERROR_MESSAGE) FROM DUMMY;		
END;

select top 1 MAX(DATE) INTO DATE_VALUE
from "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CEL_O2C_DELTA_LOG_TABLE"
where PROC_NAME = 'ADDRC';

select top 1 END_TIME INTO TIME_VALUE
from "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CEL_O2C_DELTA_LOG_TABLE"
where PROC_NAME = 'ADDRC' AND DATE = :DATE_VALUE
order by date desc;

DEPARTMENT_USER = SELECT DISTINCT EXTENSIONATTRIBUTE10, DEPARTMENT
FROM "ACQ_AD"."shell.app.dd.acq_ad::CT_S_AD_USER_ATTRIBUTES_EXT"
WHERE department <> '' AND EXTENSIONATTRIBUTE10 <> '';

-- Insert into OTC activity table
INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CEL_O2C_DELTA_ACTIVITIES_P94" (
"CASE_KEY",
"ACTIVITY_EN",
"EVENTTIME",
"SORTING",
"USER_NAME",
"USER_TYPE",
"CHANGED_TABLE",
"CHANGED_FIELD",
"CHANGED_FROM",
"CHANGED_TO",
"CHANGE_NUMBER",
"TRANSACTION_CODE",
"MANDT",
"VBELN",
"POSNR"
,"SYSTEM_ID"
,"LAST_TS"
,"LAST_ACTION_CD"
,"LAST_ACTION_BY_ID"
,"PARTITION_ID"
--Adding as a part of 2021 refdoc enhancement to get delivery, invoive, activity details of each document
,"REFDOC_INVOICE"
,"SQL_PROC_NAME"
,"CATEGORY"
,DEPARTMENT)
SELECT DISTINCT
VBAP.MANDT || VBAP.VBELN || VBAP.POSNR AS "CASE_KEY"
, 'Acc doc dispute reason code ' ||
CASE
WHEN CDPOS.VALUE_NEW <> '' THEN 'set to ' || LTRIM(CDPOS.VALUE_NEW)
WHEN CDPOS.VALUE_NEW = '' THEN 'removed'
END AS "ACTIVITY_EN"
, TO_DATE(CDHDR.UDATE, 'YYYYMMDD') || ' ' || TO_TIME(IFNULL(CDHDR.UTIME, '23:59:59'), 'HH24MISS') AS "EVENTTIME"
,91 AS "_SORTING"
,CDHDR.USERNAME AS "USER_NAME"
--Changed the usertype logic to search USR02 and USH02 tables and hardcode it to 9 if both are null.
,COALESCE (NULLIF(USR02.USTYP,''),NULLIF(USH02.USTYP,''),'9') AS "USER_TYPE"
,CDPOS.TABNAME AS "CHANGED_TABLE"
,CDPOS.FNAME AS "CHANGED_FIELD"
,LTRIM(CDPOS.VALUE_OLD) AS "CHANGED_FROM"
,LTRIM(CDPOS.VALUE_NEW) AS "CHANGED_TO"
,CDHDR.CHANGENR AS "CHANGE_NUMBER"
,CDHDR.TCODE AS "TRANSACTION_CODE"
,VBAP.MANDT AS "MANDT"
,VBAP.VBELN AS "VBELN"
,VBAP.POSNR AS "POSNR"
,'1001' AS SYSTEM_ID
,CURRENT_UTCTIMESTAMP AS LAST_TS
,'C' AS LAST_ACTION_CD
,SESSION_USER AS LAST_ACTION_BY_ID
,CHKSUM.PARTITION_ID AS "PARTITION_ID"
,BKPF_INV.BUKRS || BKPF_INV.BELNR ||  BKPF_INV.GJAHR as REFDOC_INVOICE
,'PR_GS_O2C_ADDRC_DELTA' AS SQL_PROC_NAME
,'Change' AS CATEGORY
,USER_AT.DEPARTMENT AS DEPARTMENT
FROM CDHDR AS CDHDR
INNER JOIN CDPOS AS CDPOS  ON 1=1
AND CDPOS.MANDANT = CDHDR.MANDANT
AND CDPOS.OBJECTCLAS = CDHDR.OBJECTCLAS
AND CDPOS.OBJECTID = CDHDR.OBJECTID
AND CDPOS.CHANGENR = CDHDR.CHANGENR
AND CDPOS.FNAME = 'RSTGR'
AND CDPOS.TABNAME = 'BSEG'
INNER JOIN BSEG AS BSEG_INV ON 1=1
AND BSEG_INV.MANDT = CDPOS.MANDANT
AND CDPOS.TABKEY = BSEG_INV.MANDT || BSEG_INV.BUKRS || BSEG_INV.BELNR || BSEG_INV.GJAHR || BSEG_INV.BUZEI
AND BSEG_INV.BSCHL IN ('01','11')
AND BSEG_INV.KOART = 'D'
INNER JOIN BKPF AS BKPF_INV ON 1=1
AND BSEG_INV.MANDT = BKPF_INV.MANDT
AND BSEG_INV.BUKRS = BKPF_INV.BUKRS
AND BSEG_INV.BELNR = BKPF_INV.BELNR
AND BSEG_INV.GJAHR = BKPF_INV.GJAHR
AND BKPF_INV.AWTYP = 'VBRK'
INNER JOIN VBRK AS VBRK on 1=1
AND VBRK.MANDT = BKPF_INV.MANDT
AND VBRK.VBELN = BKPF_INV.AWKEY
INNER JOIN VBRP AS VBRP ON 1=1
AND VBRP.MANDT = VBRK.MANDT
AND VBRP.VBELN = VBRK.VBELN
INNER JOIN VBAP AS VBAP ON 1=1
AND VBRP.AUBEL = VBAP.VBELN
AND VBRP.AUPOS = VBAP.POSNR
INNER JOIN VBAK AS VBAK ON 1=1
AND VBAK.VBELN = VBAP.VBELN
AND VBAK.VBTYP = 'C'
LEFT JOIN USR02 AS USR02 ON
USR02.MANDT = CDHDR.MANDANT AND
USR02.BNAME = CDHDR.USERNAME
LEFT JOIN "SHELL_CELONIS"."shell.app.dd.shell_celonis::CT_CEL_USH02" AS USH02 ON
USH02.MANDT = CDHDR.MANDANT AND
USH02.BNAME = CDHDR.USERNAME
LEFT JOIN "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CEL_O2C_CHECKSUM_TABLE" AS CHKSUM ON 1=1
AND CHKSUM."DATE" = :IP_ERDAT
LEFT JOIN :DEPARTMENT_USER AS USER_AT ON 1=1
AND USER_AT.EXTENSIONATTRIBUTE10 =  CDHDR.USERNAME
WHERE CDHDR.OBJECTCLAS = 'BELEG' AND
(TO_DATE(UDATE,'YYYYMMDD')||TO_TIME(UTIME,'HHMISS') >= TO_DATE(:DATE_VALUE,'YYYY-MM-DD')||TO_TIME(:TIME_VALUE,'HH12:MI:SS'));

SELECT MAX("PARTITION_ID") INTO V_PRTN_ID FROM  "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CEL_O2C_CHECKSUM_TABLE" ;

IF(V_PRTN_ID != '0000000000')
THEN 
INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CEL_O2C_DELTA_LOG_TABLE" VALUES ('O2C','ADDRC',CURRENT_DATE, CURRENT_UTCTIME,'Delta load');
ELSE
INSERT INTO "SHELL_CELONIS"."shell.app.dd.shell_celonis_otc_delta::CT_CEL_O2C_DELTA_LOG_TABLE" VALUES ('O2C','ADDRC',CURRENT_DATE, CURRENT_UTCTIME,'Delta full load');
END IF ;

END;
