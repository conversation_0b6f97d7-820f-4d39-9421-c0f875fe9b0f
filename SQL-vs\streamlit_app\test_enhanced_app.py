#!/usr/bin/env python3
"""
Test Enhanced App
=================

Test the enhanced app with LLM table analysis.
"""

def test_enhanced_app():
    """Test the enhanced app"""
    print("🔍 Testing Enhanced App...")
    
    try:
        # Test imports
        print("1. Testing imports...")
        import app
        print("   ✅ App imported successfully")
        
        from sql_parser import AdvancedSQLParser
        print("   ✅ SQL parser imported")
        
        from llm_service import LLMService
        print("   ✅ LLM service imported")
        
        # Test app initialization
        print("\n2. Testing app initialization...")
        analyzer_app = app.SAP_HANA_Analyzer_App()
        print("   ✅ App initialized successfully")
        
        # Test LLM table analysis method
        print("\n3. Testing LLM table analysis method...")
        
        # Mock cross analysis data
        mock_cross_analysis = {
            'table_consolidation': {
                'BSEG': {
                    'used_in_procedures': ['vendor_payment_analysis'],
                    'all_columns': ['LIFNR', 'DMBTR', 'KOART', 'BELNR'],
                    'consolidated_filters': {
                        'has_filters': True,
                        'patterns': [
                            {
                                'column': 'LIFNR',
                                'unique_values': ['IS NOT NULL'],
                                'example_conditions': ['I.LIFNR IS NOT NULL']
                            }
                        ]
                    },
                    'consolidated_joins': {
                        'has_joins': True,
                        'relationships': [
                            {
                                'related_table': 'BKPF',
                                'example_condition': 'h.belnr = i.belnr'
                            }
                        ]
                    },
                    'migration_priority': 'HIGH'
                }
            },
            'filter_analysis': {},
            'join_analysis': {}
        }
        
        # Test prompt creation
        prompt = analyzer_app._create_table_analysis_prompt(
            mock_cross_analysis['table_consolidation'],
            mock_cross_analysis['filter_analysis'],
            mock_cross_analysis['join_analysis']
        )
        
        print("   ✅ Table analysis prompt created")
        print(f"   Prompt length: {len(prompt)} characters")
        print(f"   Contains BSEG: {'BSEG' in prompt}")
        print(f"   Contains ANTLR: {'ANTLR' in prompt}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run enhanced app test"""
    print("🧪 Enhanced App Test")
    print("=" * 25)
    
    if test_enhanced_app():
        print("\n✅ Enhanced app test passed!")
        print("🚀 Ready to run Streamlit with LLM table analysis")
        print("\nTo start the app:")
        print("   streamlit run app.py")
    else:
        print("\n❌ Enhanced app test failed!")

if __name__ == "__main__":
    main()
