#!/usr/bin/env python3
"""
Test BSEG Analysis
==================

Test the improved parser with the vendor_payment_analysis.sql that contains BSEG table
to ensure filters and joins are properly captured.
"""

def test_bseg_procedure():
    """Test BSEG procedure analysis"""
    print("🔍 Testing BSEG Procedure Analysis...")
    
    try:
        from sql_parser import AdvancedSQLParser
        
        parser = AdvancedSQLParser()
        
        # Load the vendor payment analysis SQL
        with open('../procedures/vendor_payment_analysis.sql', 'r') as f:
            bseg_sql = f.read()
        
        print("📝 Parsing vendor_payment_analysis.sql...")
        result = parser.parse_procedure(bseg_sql, "vendor_payment_analysis")
        
        print(f"\n📊 Results:")
        print(f"   Tables: {list(result.tables.keys())}")
        print(f"   Filters: {len(result.filters)}")
        print(f"   Joins: {len(result.joins)}")
        
        # Check if BSEG is found
        if 'BSEG' in result.tables:
            print(f"\n✅ BSEG table found!")
            bseg_table = result.tables['BSEG']
            print(f"   BSEG columns: {list(bseg_table.columns)}")
        else:
            print(f"\n❌ BSEG table NOT found!")
            print(f"   Available tables: {list(result.tables.keys())}")
        
        # Check filters
        print(f"\n🔍 Filter Details:")
        for i, filter_cond in enumerate(result.filters):
            print(f"   Filter {i+1}: {filter_cond.table}.{filter_cond.column} {filter_cond.operator} {filter_cond.values}")
        
        # Check joins
        print(f"\n🔗 Join Details:")
        for i, join in enumerate(result.joins):
            print(f"   Join {i+1}: {join.left_table} {join.join_type} {join.right_table} ON {join.condition}")
        
        # Test with multiple procedures including BSEG
        print(f"\n🔄 Testing Cross-Procedure Analysis with BSEG...")
        
        # Add a second procedure that also uses BSEG but with different filters
        second_sql = '''
        CREATE PROCEDURE test_bseg_2 AS
        BEGIN
        SELECT BSEG.BELNR, BSEG.BUZEI, BKPF.BLDAT
        FROM BSEG
        INNER JOIN BKPF ON BSEG.BELNR = BKPF.BELNR
        WHERE BSEG.KOART = 'D'
        AND BSEG.SHKZG = 'S';
        END;
        '''
        
        result2 = parser.parse_procedure(second_sql, "test_bseg_2")
        
        # Cross-procedure analysis
        procedures = [result, result2]
        cross_analysis = parser.analyze_cross_procedure_patterns(procedures)
        
        # Check table consolidation for BSEG
        table_consolidation = cross_analysis.get('table_consolidation', {})
        if 'BSEG' in table_consolidation:
            print(f"\n📋 BSEG Table Consolidation:")
            bseg_data = table_consolidation['BSEG']
            print(f"   Used in procedures: {bseg_data['used_in_procedures']}")
            print(f"   All columns: {bseg_data['all_columns']}")
            print(f"   Migration priority: {bseg_data['migration_priority']}")
            
            # Check filter consolidation
            filter_info = bseg_data['consolidated_filters']
            if filter_info['has_filters']:
                print(f"   Filter patterns:")
                for pattern in filter_info['patterns']:
                    print(f"     - {pattern['column']}: {pattern['unique_values']} ({pattern['migration_note']})")
        
        # Check filter analysis recommendations
        filter_analysis = cross_analysis.get('filter_analysis', {})
        recommendations = filter_analysis.get('recommendations', [])
        if recommendations:
            print(f"\n⚠️ Migration Recommendations:")
            for rec in recommendations:
                print(f"   {rec}")
        
        return True
        
    except Exception as e:
        print(f"❌ BSEG test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run BSEG test"""
    print("🧪 BSEG Analysis Test")
    print("=" * 30)
    
    if test_bseg_procedure():
        print("\n✅ BSEG test completed!")
    else:
        print("\n❌ BSEG test failed!")

if __name__ == "__main__":
    main()
