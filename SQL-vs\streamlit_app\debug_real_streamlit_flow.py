#!/usr/bin/env python3
"""
Debug Real Streamlit Flow
=========================

Debug the exact flow that Streamlit uses to see why BSEG values are not showing.
"""

def debug_streamlit_flow():
    """Debug the exact Streamlit parsing flow"""
    print("🔍 Debugging Real Streamlit Flow...")
    
    try:
        from sql_parser import AdvancedSQLParser
        import pandas as pd
        
        parser = AdvancedSQLParser()
        
        # Read the actual vendor_payment_analysis.sql file
        try:
            with open('../procedures/vendor_payment_analysis.sql', 'r') as f:
                real_sql = f.read()
            print("✅ Loaded real vendor_payment_analysis.sql")
        except:
            # Fallback to a realistic BSEG procedure
            real_sql = '''
            CREATE PROCEDURE vendor_payment_analysis(
                IN p_company_code NVARCHAR(4),
                IN p_fiscal_year NVARCHAR(4)
            )
            LANGUAGE SQLSCRIPT
            SQL SECURITY INVOKER
            READS SQL DATA AS
            BEGIN
                SELECT 
                    h.bukrs as company_code,
                    h.belnr as document_number,
                    h.gjahr as fiscal_year,
                    h.bldat as document_date,
                    h.budat as posting_date,
                    i.lifnr as vendor_number,
                    v.name1 as vendor_name,
                    i.dmbtr as amount_local_currency,
                    i.wrbtr as amount_document_currency,
                    i.waers as currency,
                    i.zfbdt as baseline_date,
                    i.zbd1t as cash_discount_days,
                    i.zbd2t as payment_terms_days,
                    CASE 
                        WHEN DAYS_BETWEEN(i.zfbdt, CURRENT_DATE) <= i.zbd1t 
                        THEN 'DISCOUNT_PERIOD'
                        WHEN DAYS_BETWEEN(i.zfbdt, CURRENT_DATE) <= i.zbd2t 
                        THEN 'PAYMENT_PERIOD'
                        ELSE 'OVERDUE'
                    END as payment_status
                FROM bkpf h
                INNER JOIN bseg i ON h.belnr = i.belnr 
                                 AND h.gjahr = i.gjahr 
                                 AND h.bukrs = i.bukrs
                INNER JOIN lfa1 v ON i.lifnr = v.lifnr
                WHERE h.bukrs = p_company_code
                  AND h.gjahr = p_fiscal_year
                  AND i.koart = 'K'
                  AND i.lifnr IS NOT NULL
                ORDER BY h.budat DESC, i.lifnr;
            END;
            '''
            print("⚠️ Using fallback SQL (real file not found)")
        
        print(f"\n📝 SQL Content Preview:")
        print(f"   Length: {len(real_sql)} characters")
        print(f"   Contains 'bseg': {'bseg' in real_sql.lower()}")
        print(f"   Contains 'BSEG': {'BSEG' in real_sql}")
        print(f"   Contains 'i.': {'i.' in real_sql}")
        
        # Step 1: Parse the procedure (exactly like Streamlit does)
        print(f"\n🔧 Step 1: Parse Procedure")
        result = parser.parse_procedure(real_sql, "vendor_payment_analysis")
        
        print(f"   Tables found: {list(result.tables.keys())}")
        print(f"   Filters found: {len(result.filters)}")
        print(f"   Joins found: {len(result.joins)}")
        
        # Check each table's details
        for table_name, table_ref in result.tables.items():
            print(f"\n   Table: {table_name}")
            print(f"     Columns: {sorted(list(table_ref.columns))}")
            print(f"     Usage contexts: {table_ref.usage_contexts}")
        
        # Step 2: Cross-procedure analysis (like Streamlit does)
        print(f"\n🔧 Step 2: Cross-Procedure Analysis")
        procedures = [result]
        cross_analysis = parser.analyze_cross_procedure_patterns(procedures)
        
        # Step 3: Create table analysis dataframe (exactly like Streamlit)
        print(f"\n🔧 Step 3: Create Table Analysis DataFrame")
        
        # Simulate the Streamlit _create_table_analysis_dataframe method
        table_consolidation = cross_analysis.get('table_consolidation', {})
        
        if not table_consolidation:
            print("❌ No table consolidation data!")
            return False
        
        rows = []
        for table_name, table_data in table_consolidation.items():
            # Format procedures using this table
            procedures_str = ", ".join(table_data['used_in_procedures'])
            
            # Format all columns used across procedures
            columns_str = ", ".join([f"{table_name}.{col}" for col in table_data['all_columns']])
            
            # Format usage contexts
            used_as = ", ".join(table_data['usage_contexts'])
            
            # Consolidate filter conditions
            filter_info = table_data['consolidated_filters']
            if filter_info['has_filters']:
                filter_patterns = []
                for pattern in filter_info['patterns']:
                    example = pattern['example_conditions'][0] if pattern['example_conditions'] else ""
                    filter_patterns.append(f"{pattern['column']}: {example}")
                filter_conditions = " | ".join(filter_patterns)
            else:
                filter_conditions = ""
            
            # Consolidate join conditions
            join_info = table_data['consolidated_joins']
            if join_info['has_joins']:
                join_patterns = []
                for rel in join_info['relationships']:
                    join_patterns.append(f"→ {rel['related_table']}: {rel['example_condition']}")
                join_conditions = " | ".join(join_patterns)
            else:
                join_conditions = ""
            
            # Enhanced notes with migration info
            notes = f"{table_data['table_purpose']} | Used in {table_data['procedure_count']} procedures | Priority: {table_data['migration_priority']}"
            
            row = {
                'Table Name': table_name,
                'Used in Procedures': procedures_str,
                'Procedure Count': table_data['procedure_count'],
                'All Columns Used': columns_str,
                'Usage Contexts': used_as,
                'Consolidated Filters': filter_conditions,
                'Consolidated Joins': join_conditions,
                'Migration Priority': table_data['migration_priority'],
                'Notes': notes
            }
            rows.append(row)
        
        # Create DataFrame
        df = pd.DataFrame(rows)
        
        print(f"\n📊 Final DataFrame (what you see in Streamlit):")
        if not df.empty:
            for index, row in df.iterrows():
                print(f"\n   Row {index + 1}:")
                for col, value in row.items():
                    print(f"     {col}: {value}")
        else:
            print("❌ DataFrame is empty!")
        
        # Specific BSEG check
        bseg_row = df[df['Table Name'] == 'BSEG'] if not df.empty else pd.DataFrame()
        if not bseg_row.empty:
            print(f"\n✅ BSEG Row Found:")
            bseg_data = bseg_row.iloc[0]
            print(f"   Columns: {bseg_data['All Columns Used']}")
            print(f"   Filters: {bseg_data['Consolidated Filters']}")
            print(f"   Joins: {bseg_data['Consolidated Joins']}")
        else:
            print(f"\n❌ BSEG Row NOT Found in final DataFrame!")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run Streamlit flow debug"""
    print("🐛 Streamlit Flow Debug")
    print("=" * 30)
    
    if debug_streamlit_flow():
        print("\n✅ Debug completed!")
    else:
        print("\n❌ Debug failed!")

if __name__ == "__main__":
    main()
