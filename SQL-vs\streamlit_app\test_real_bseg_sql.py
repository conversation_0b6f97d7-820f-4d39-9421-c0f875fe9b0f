#!/usr/bin/env python3
"""
Test Real BSEG SQL
==================

Test the enhanced parser with the actual BSEG SQL provided by the user.
"""

def test_real_bseg_sql():
    """Test with the real BSEG SQL"""
    print("🔍 Testing Real BSEG SQL...")
    
    try:
        from sql_parser import AdvancedSQLParser
        
        parser = AdvancedSQLParser()
        
        # The actual BSEG SQL from the user
        real_bseg_sql = '''
CREATE PROCEDURE PR_BSEG_ACTIVITY_LOAD
(
    IN IP_BUDAT_FROM DATE,
    IN IP_BUDAT_TO DATE,
    IN IP_BUKRS VARCHAR(4)
)
LANGUAGE SQL
AS
BEGIN
    /*
    Purpose: Extract BSEG activities for OTC analysis
    Tables: BSEG (FI Document Line Items)
    */
    
    TRUNCATE TABLE CT_BSEG_DELTA_ACTIVITIES;
    
    INSERT INTO CT_BSEG_DELTA_ACTIVITIES
    (
        MANDT,
        BUKRS,
        BELNR,
        GJAHR,
        BUZEI,
        BUDAT,
        BLART,
        TCODE,
        SHKZG,
        WRBTR,
        PARTITION_ID,
        CREATED_AT
    )
    SELECT 
        MANDT,
        BUKRS,
        BELNR,
        GJAHR,
        BUZEI,
        BUDAT,
        BLART,
        TCODE,
        SHKZG,
        WRBTR,
        1 as PARTITION_ID,
        CURRENT_TIMESTAMP as CREATED_AT
    FROM BSEG
    WHERE BUDAT BETWEEN :IP_BUDAT_FROM AND :IP_BUDAT_TO
    AND BUKRS = :IP_BUKRS
    AND BLART IN ('SA', 'KR', 'DR');
END;
        '''
        
        print("📝 Parsing real BSEG procedure...")
        result = parser.parse_procedure(real_bseg_sql, "PR_BSEG_ACTIVITY_LOAD")
        
        print(f"\n📊 Real BSEG Parsing Results:")
        print(f"   Tables found: {list(result.tables.keys())}")
        print(f"   Filters found: {len(result.filters)}")
        print(f"   Joins found: {len(result.joins)}")
        print(f"   Parameters found: {len(result.parameters)}")
        
        # Check each table's details
        for table_name, table_ref in result.tables.items():
            print(f"\n🔹 Table: {table_name}")
            print(f"   Columns: {sorted(list(table_ref.columns))}")
            print(f"   Usage contexts: {table_ref.usage_contexts}")
        
        # Check BSEG specifically
        if 'BSEG' in result.tables:
            bseg_table = result.tables['BSEG']
            bseg_columns = sorted(list(bseg_table.columns))
            print(f"\n✅ BSEG Table Found!")
            print(f"   BSEG columns: {bseg_columns}")
            
            # Expected columns from the SELECT statement
            expected_columns = ['MANDT', 'BUKRS', 'BELNR', 'GJAHR', 'BUZEI', 'BUDAT', 'BLART', 'TCODE', 'SHKZG', 'WRBTR']
            found_expected = [col for col in expected_columns if col in bseg_columns]
            missing_expected = [col for col in expected_columns if col not in bseg_columns]
            
            print(f"   Expected columns: {expected_columns}")
            print(f"   Found expected: {found_expected}")
            if missing_expected:
                print(f"   Missing expected: {missing_expected}")
            else:
                print(f"   ✅ All expected columns found!")
        else:
            print(f"\n❌ BSEG Table NOT Found!")
        
        # Check CT_BSEG_DELTA_ACTIVITIES table
        if 'CT_BSEG_DELTA_ACTIVITIES' in result.tables:
            ct_table = result.tables['CT_BSEG_DELTA_ACTIVITIES']
            ct_columns = sorted(list(ct_table.columns))
            print(f"\n🔹 CT_BSEG_DELTA_ACTIVITIES Table Found!")
            print(f"   Columns: {ct_columns}")
        
        # Check filters
        print(f"\n🔍 Filter Details:")
        for i, filter_cond in enumerate(result.filters):
            print(f"   Filter {i+1}: {filter_cond.table}.{filter_cond.column} {filter_cond.operator} {filter_cond.values}")
        
        # Check parameters
        print(f"\n📋 Parameter Details:")
        for i, param in enumerate(result.parameters):
            print(f"   Parameter {i+1}: {param.name} ({param.data_type})")
        
        # Test cross-procedure analysis
        print(f"\n🔄 Testing Cross-Procedure Analysis...")
        procedures = [result]
        cross_analysis = parser.analyze_cross_procedure_patterns(procedures)
        
        # Check table consolidation for BSEG
        table_consolidation = cross_analysis.get('table_consolidation', {})
        if 'BSEG' in table_consolidation:
            print(f"\n📋 BSEG Table Consolidation:")
            bseg_data = table_consolidation['BSEG']
            print(f"   Used in procedures: {bseg_data['used_in_procedures']}")
            print(f"   All columns: {sorted(bseg_data['all_columns'])}")
            print(f"   Migration priority: {bseg_data['migration_priority']}")
            print(f"   Table purpose: {bseg_data['table_purpose']}")
            
            # Check filter consolidation
            filter_info = bseg_data['consolidated_filters']
            if filter_info['has_filters']:
                print(f"   Filter patterns:")
                for pattern in filter_info['patterns']:
                    print(f"     - {pattern['column']}: {pattern['unique_values']} ({pattern['migration_note']})")
            else:
                print(f"   No filters found")
        else:
            print(f"\n❌ BSEG NOT found in table consolidation!")
        
        # Create a sample table analysis dataframe like Streamlit would
        print(f"\n📊 Sample Table Analysis Output (like Streamlit):")
        if 'BSEG' in table_consolidation:
            bseg_data = table_consolidation['BSEG']
            
            # Format like Streamlit does
            procedures_str = ", ".join(bseg_data['used_in_procedures'])
            columns_str = ", ".join([f"BSEG.{col}" for col in sorted(bseg_data['all_columns'])])
            
            print(f"   Table Name: BSEG")
            print(f"   Used in Procedures: {procedures_str}")
            print(f"   All Columns Used: {columns_str}")
            print(f"   Migration Priority: {bseg_data['migration_priority']}")
            print(f"   Notes: {bseg_data['table_purpose']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Real BSEG test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run real BSEG test"""
    print("🧪 Real BSEG SQL Test")
    print("=" * 25)
    
    if test_real_bseg_sql():
        print("\n✅ Real BSEG test completed!")
        print("🚀 Your BSEG procedure should now be properly parsed")
        print("\nNext steps:")
        print("1. Start Streamlit: streamlit run app.py")
        print("2. Upload your PR_BSEG_ACTIVITY_LOAD.sql file")
        print("3. Check the Table Analysis tab")
        print("4. Click 'Generate LLM Table Analysis'")
    else:
        print("\n❌ Real BSEG test failed!")

if __name__ == "__main__":
    main()
