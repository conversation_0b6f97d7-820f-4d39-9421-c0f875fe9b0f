#!/usr/bin/env python3
"""
Debug LLM Input
===============

Check what data the LLM is actually receiving to understand if ANTLR abstraction is working.
"""

import json

def debug_llm_input():
    """Debug what data flows to the LLM"""
    print("🔍 Debugging LLM Input Data...")
    
    try:
        from sql_parser import AdvancedSQLParser
        
        parser = AdvancedSQLParser()
        
        # Sample SQL with clear filters
        sample_sql = '''
        PROCEDURE "PR_ORDER_DELIVERY_ANALYSIS" AS
        BEGIN
        SELECT VBAK.VBELN, VBAK.AUART, VBAP.POSNR
        FROM VBAK 
        INNER JOIN VBAP ON VBAK.VBELN = VBAP.VBELN
        WHERE VBAK.AUART IN ('OR', 'TA', 'ZOR')
        AND VBAP.ERDAT > '20240101';
        END;
        '''
        
        print("📝 Parsing sample SQL...")
        result = parser.parse_procedure(sample_sql, "PR_ORDER_DELIVERY_ANALYSIS")
        
        print(f"\n📊 Parsed Results:")
        print(f"   Tables: {list(result.tables.keys())}")
        print(f"   Filters: {len(result.filters)}")
        print(f"   Joins: {len(result.joins)}")
        
        # Debug filter details
        print(f"\n🔍 Filter Details:")
        for i, filter_cond in enumerate(result.filters):
            print(f"   Filter {i+1}:")
            print(f"     Table: {filter_cond.table}")
            print(f"     Column: {filter_cond.column}")
            print(f"     Operator: {filter_cond.operator}")
            print(f"     Values: {filter_cond.values}")
            print(f"     Condition: {filter_cond.condition_text}")
        
        # Test cross-procedure analysis
        print(f"\n🔄 Testing Cross-Procedure Analysis...")
        procedures = [result]
        cross_analysis = parser.analyze_cross_procedure_patterns(procedures)
        
        # Debug table consolidation
        table_consolidation = cross_analysis.get('table_consolidation', {})
        print(f"\n📋 Table Consolidation:")
        for table_name, table_data in table_consolidation.items():
            print(f"   Table: {table_name}")
            print(f"     Procedures: {table_data['used_in_procedures']}")
            print(f"     Columns: {table_data['all_columns']}")
            
            # Debug filter consolidation
            filter_info = table_data['consolidated_filters']
            if filter_info['has_filters']:
                print(f"     Filter Patterns:")
                for pattern in filter_info['patterns']:
                    print(f"       Column: {pattern['column']}")
                    print(f"       Values: {pattern['unique_values']}")
                    print(f"       Examples: {pattern['example_conditions']}")
        
        # Debug what would go to LLM
        print(f"\n🤖 Data for LLM:")
        llm_data = {
            'summary': cross_analysis.get('summary', {}),
            'table_consolidation': table_consolidation,
            'filter_analysis': cross_analysis.get('filter_analysis', {}),
            'join_analysis': cross_analysis.get('join_analysis', {})
        }
        
        print(json.dumps(llm_data, indent=2, default=str))
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run debug"""
    print("🐛 LLM Input Debug")
    print("=" * 30)
    
    if debug_llm_input():
        print("\n✅ Debug completed!")
    else:
        print("\n❌ Debug failed!")

if __name__ == "__main__":
    main()
