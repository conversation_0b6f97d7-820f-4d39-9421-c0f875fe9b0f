#!/usr/bin/env python3
"""
SAP HANA SQL Procedure Analyzer
===============================

This script analyzes SAP HANA SQL procedures to extract:
- Tables and their schemas
- Columns used in SELECT, WHERE, JOIN clauses
- Join relationships and conditions
- Filter conditions and parameters
- Input/Output parameters

The extracted metadata is formatted for LLM consumption to generate
requirements documentation.
"""

import os
import sys
import json
import re
from pathlib import Path
from typing import Dict, List, Set, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict

# Add generated ANTLR files to path
sys.path.append(str(Path(__file__).parent / "generated"))

try:
    from antlr4 import *
    from HanaLexer import HanaLexer
    from HanaParser import HanaParser
    from HanaListener import HanaListener
except ImportError as e:
    print(f"❌ ANTLR4 imports failed: {e}")
    print("Please run sql_parser_setup.py first to generate the parser files.")
    sys.exit(1)

@dataclass
class TableReference:
    """Represents a table reference in SQL"""
    name: str
    schema: Optional[str] = None
    alias: Optional[str] = None
    full_name: Optional[str] = None
    
    def __post_init__(self):
        if self.full_name is None:
            if self.schema:
                self.full_name = f"{self.schema}.{self.name}"
            else:
                self.full_name = self.name

@dataclass
class ColumnReference:
    """Represents a column reference in SQL"""
    name: str
    table: Optional[str] = None
    table_alias: Optional[str] = None
    context: Optional[str] = None  # SELECT, WHERE, JOIN, etc.

@dataclass
class JoinClause:
    """Represents a JOIN clause"""
    join_type: str
    left_table: str
    right_table: str
    condition: str
    line_number: Optional[int] = None

@dataclass
class WhereCondition:
    """Represents a WHERE condition"""
    column: str
    operator: str
    value: str
    table: Optional[str] = None
    line_number: Optional[int] = None

@dataclass
class ProcedureAnalysis:
    """Complete analysis of a SQL procedure"""
    procedure_name: str
    file_path: str
    tables: List[TableReference]
    columns: List[ColumnReference]
    joins: List[JoinClause]
    where_conditions: List[WhereCondition]
    input_parameters: List[Dict[str, Any]]
    output_parameters: List[Dict[str, Any]]
    raw_sql: str
    analysis_summary: Dict[str, Any]

class HanaAnalysisListener(HanaListener):
    """Custom listener to extract metadata from parsed SQL"""
    
    def __init__(self):
        self.tables = []
        self.columns = []
        self.joins = []
        self.where_conditions = []
        self.parameters = []
        self.current_context = None
        self.table_aliases = {}  # alias -> table_name mapping
    
    def enterTable_name(self, ctx):
        """Extract table names"""
        table_text = ctx.getText()
        
        # Parse schema.table format
        parts = table_text.split('.')
        if len(parts) == 2:
            schema, table = parts
            table_ref = TableReference(name=table, schema=schema, full_name=table_text)
        else:
            table_ref = TableReference(name=table_text, full_name=table_text)
        
        self.tables.append(table_ref)
    
    def enterTableview_name(self, ctx):
        """Extract table/view names"""
        table_text = ctx.getText()
        
        # Parse schema.table format
        parts = table_text.split('.')
        if len(parts) == 2:
            schema, table = parts
            table_ref = TableReference(name=table, schema=schema, full_name=table_text)
        else:
            table_ref = TableReference(name=table_text, full_name=table_text)
        
        self.tables.append(table_ref)
    
    def enterColumn_name(self, ctx):
        """Extract column names"""
        column_text = ctx.getText()
        
        # Parse table.column format
        parts = column_text.split('.')
        if len(parts) == 2:
            table, column = parts
            col_ref = ColumnReference(name=column, table=table, context=self.current_context)
        else:
            col_ref = ColumnReference(name=column_text, context=self.current_context)
        
        self.columns.append(col_ref)
    
    def enterJoin_clause(self, ctx):
        """Extract JOIN clauses"""
        self.current_context = "JOIN"
        
        # Extract join type and tables
        join_text = ctx.getText()
        
        # This is a simplified extraction - in practice, you'd need more sophisticated parsing
        join_info = JoinClause(
            join_type="INNER",  # Default, should be parsed from context
            left_table="",      # Should be extracted from context
            right_table="",     # Should be extracted from context
            condition=join_text
        )
        self.joins.append(join_info)
    
    def enterWhere_clause(self, ctx):
        """Extract WHERE conditions"""
        self.current_context = "WHERE"
        
        where_text = ctx.getText()
        # Simplified condition extraction
        condition = WhereCondition(
            column="",
            operator="",
            value="",
            table=None
        )
        self.where_conditions.append(condition)
    
    def enterParameter(self, ctx):
        """Extract procedure parameters"""
        param_text = ctx.getText()
        # Parse parameter information
        param_info = {
            "name": param_text,
            "type": "unknown",
            "direction": "IN"  # IN, OUT, INOUT
        }
        self.parameters.append(param_info)

class SQLProcedureAnalyzer:
    """Main analyzer class for SQL procedures"""
    
    def __init__(self, procedures_dir: str = "procedures",
                 output_dir: str = "analysis_output"):
        self.procedures_dir = Path(procedures_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        self.all_analyses = []
        self.summary_stats = defaultdict(int)
    
    def parse_sql_file(self, file_path: Path) -> Optional[ProcedureAnalysis]:
        """Parse a single SQL file and extract metadata"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # Create ANTLR input stream
            input_stream = InputStream(sql_content)
            lexer = HanaLexer(input_stream)
            stream = CommonTokenStream(lexer)
            parser = HanaParser(stream)
            
            # Parse the SQL
            tree = parser.compilation_unit()
            
            # Walk the parse tree with our listener
            listener = HanaAnalysisListener()
            walker = ParseTreeWalker()
            walker.walk(listener, tree)
            
            # Create analysis result
            analysis = ProcedureAnalysis(
                procedure_name=file_path.stem,
                file_path=str(file_path),
                tables=listener.tables,
                columns=listener.columns,
                joins=listener.joins,
                where_conditions=listener.where_conditions,
                input_parameters=[p for p in listener.parameters if p.get('direction') in ['IN', 'INOUT']],
                output_parameters=[p for p in listener.parameters if p.get('direction') in ['OUT', 'INOUT']],
                raw_sql=sql_content,
                analysis_summary=self._create_summary(listener)
            )
            
            return analysis
            
        except Exception as e:
            print(f"❌ Error parsing {file_path}: {e}")
            return None
    
    def _create_summary(self, listener: HanaAnalysisListener) -> Dict[str, Any]:
        """Create summary statistics for a procedure"""
        return {
            "table_count": len(set(t.full_name for t in listener.tables)),
            "column_count": len(listener.columns),
            "join_count": len(listener.joins),
            "where_condition_count": len(listener.where_conditions),
            "parameter_count": len(listener.parameters),
            "unique_tables": list(set(t.full_name for t in listener.tables)),
            "unique_columns": list(set(c.name for c in listener.columns))
        }
    
    def analyze_all_procedures(self) -> List[ProcedureAnalysis]:
        """Analyze all SQL procedures in the directory"""
        print(f"Analyzing SQL procedures in: {self.procedures_dir}")
        
        sql_files = list(self.procedures_dir.glob("*.sql")) + \
                   list(self.procedures_dir.glob("*.SQL"))
        
        if not sql_files:
            print(f"❌ No SQL files found in {self.procedures_dir}")
            return []
        
        print(f"Found {len(sql_files)} SQL files")
        
        analyses = []
        for sql_file in sql_files:
            print(f"Analyzing: {sql_file.name}")
            analysis = self.parse_sql_file(sql_file)
            if analysis:
                analyses.append(analysis)
                self._update_summary_stats(analysis)
        
        self.all_analyses = analyses
        return analyses
    
    def _update_summary_stats(self, analysis: ProcedureAnalysis):
        """Update overall summary statistics"""
        self.summary_stats['total_procedures'] += 1
        self.summary_stats['total_tables'] += len(analysis.tables)
        self.summary_stats['total_columns'] += len(analysis.columns)
        self.summary_stats['total_joins'] += len(analysis.joins)
        self.summary_stats['total_conditions'] += len(analysis.where_conditions)

def main():
    """Main function"""
    analyzer = SQLProcedureAnalyzer()
    
    print("SAP HANA SQL Procedure Analyzer")
    print("=" * 40)
    
    # Check if procedures directory exists
    if not analyzer.procedures_dir.exists():
        print(f"❌ Procedures directory not found: {analyzer.procedures_dir}")
        print("Please create the directory and add your SQL procedure files.")
        return
    
    # Analyze all procedures
    analyses = analyzer.analyze_all_procedures()
    
    if not analyses:
        print("❌ No procedures were successfully analyzed.")
        return
    
    print(f"\n✅ Successfully analyzed {len(analyses)} procedures")
    print(f"Summary statistics: {dict(analyzer.summary_stats)}")

if __name__ == "__main__":
    main()
