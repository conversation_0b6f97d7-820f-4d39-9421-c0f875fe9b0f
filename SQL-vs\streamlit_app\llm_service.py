"""
Robust LLM Service with Azure OpenAI and Google Gemini Support
=============================================================

This module provides a unified interface for multiple LLM providers
with robust error handling, retry logic, and fallback mechanisms.
"""

import os
import time
import json
import requests
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from google import genai
from google.genai import types
import streamlit as st
from config import Config

@dataclass
class LLMResponse:
    """Standardized LLM response"""
    content: str
    provider: str
    model: str
    success: bool
    error: Optional[str] = None
    tokens_used: Optional[int] = None
    response_time: Optional[float] = None

class LLMService:
    """Unified LLM service supporting multiple providers"""
    
    def __init__(self):
        self.config = Config()
        self.providers = {
            'azure': self._call_azure_openai,
            'gemini': self._call_gemini
        }
        
        # Initialize clients
        self.azure_headers = {
            "Authorization": f"Bearer {self.config.AZURE_OPENAI_API_KEY}",
            "Content-Type": "application/json"
        }
        
        self.gemini_client = None
        if self.config.GEMINI_API_KEY:
            try:
                self.gemini_client = genai.Client(api_key=self.config.GEMINI_API_KEY)
            except Exception as e:
                st.warning(f"Failed to initialize Gemini client: {e}")
    
    def generate_requirements(self, 
                            analysis_data: Dict[str, Any], 
                            provider: str = "azure",
                            custom_prompt: Optional[str] = None) -> LLMResponse:
        """Generate requirements documentation from SQL analysis"""
        
        # Prepare the prompt
        system_prompt = self._get_system_prompt()
        user_prompt = custom_prompt or self._get_default_user_prompt(analysis_data)
        
        # Try primary provider
        response = self._call_llm_with_retry(provider, system_prompt, user_prompt)
        
        # Fallback to other provider if primary fails
        if not response.success:
            fallback_provider = 'gemini' if provider == 'azure' else 'azure'
            st.warning(f"Primary LLM ({provider}) failed, trying fallback ({fallback_provider})")
            response = self._call_llm_with_retry(fallback_provider, system_prompt, user_prompt)
        
        return response
    
    def _call_llm_with_retry(self, provider: str, system_prompt: str, user_prompt: str, max_retries: int = 3) -> LLMResponse:
        """Call LLM with retry logic"""
        
        if provider not in self.providers:
            return LLMResponse(
                content="", 
                provider=provider, 
                model="unknown", 
                success=False, 
                error=f"Unsupported provider: {provider}"
            )
        
        for attempt in range(max_retries):
            try:
                start_time = time.time()
                response = self.providers[provider](system_prompt, user_prompt)
                response.response_time = time.time() - start_time
                
                if response.success:
                    return response
                
                # Wait before retry
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                    
            except Exception as e:
                if attempt == max_retries - 1:
                    return LLMResponse(
                        content="", 
                        provider=provider, 
                        model="unknown", 
                        success=False, 
                        error=f"Failed after {max_retries} attempts: {str(e)}"
                    )
                time.sleep(2 ** attempt)
        
        return LLMResponse(
            content="", 
            provider=provider, 
            model="unknown", 
            success=False, 
            error="Max retries exceeded"
        )
    
    def _call_azure_openai(self, system_prompt: str, user_prompt: str) -> LLMResponse:
        """Call Azure OpenAI API"""
        
        if not self.config.AZURE_OPENAI_API_KEY:
            return LLMResponse(
                content="", 
                provider="azure", 
                model=self.config.AZURE_OPENAI_MODEL, 
                success=False, 
                error="Azure OpenAI API key not configured"
            )
        
        try:
            data = {
                "model": self.config.AZURE_OPENAI_MODEL,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "max_tokens": 4000,
                "temperature": 0.7
            }
            
            response = requests.post(
                f"{self.config.AZURE_OPENAI_ENDPOINT}/chat/completions",
                headers=self.azure_headers,
                json=data,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                tokens_used = result.get('usage', {}).get('total_tokens', 0)
                
                return LLMResponse(
                    content=content,
                    provider="azure",
                    model=self.config.AZURE_OPENAI_MODEL,
                    success=True,
                    tokens_used=tokens_used
                )
            else:
                return LLMResponse(
                    content="", 
                    provider="azure", 
                    model=self.config.AZURE_OPENAI_MODEL, 
                    success=False, 
                    error=f"API Error: {response.status_code} - {response.text}"
                )
                
        except Exception as e:
            return LLMResponse(
                content="", 
                provider="azure", 
                model=self.config.AZURE_OPENAI_MODEL, 
                success=False, 
                error=str(e)
            )
    
    def _call_gemini(self, system_prompt: str, user_prompt: str) -> LLMResponse:
        """Call Google Gemini API"""
        
        if not self.gemini_client:
            return LLMResponse(
                content="", 
                provider="gemini", 
                model=self.config.GEMINI_MODEL, 
                success=False, 
                error="Gemini client not initialized"
            )
        
        try:
            # Combine system and user prompts for Gemini
            combined_prompt = f"{system_prompt}\n\n{user_prompt}"
            
            contents = [
                types.Content(
                    role="user",
                    parts=[types.Part.from_text(text=combined_prompt)],
                ),
            ]
            
            generate_content_config = types.GenerateContentConfig(
                response_mime_type="text/plain",
                max_output_tokens=4000,
                temperature=0.7
            )
            
            # Use streaming for better handling
            response_text = ""
            for chunk in self.gemini_client.models.generate_content_stream(
                model=self.config.GEMINI_MODEL,
                contents=contents,
                config=generate_content_config,
            ):
                if chunk.text:
                    response_text += chunk.text
            
            return LLMResponse(
                content=response_text,
                provider="gemini",
                model=self.config.GEMINI_MODEL,
                success=True
            )
            
        except Exception as e:
            return LLMResponse(
                content="", 
                provider="gemini", 
                model=self.config.GEMINI_MODEL, 
                success=False, 
                error=str(e)
            )
    
    def _get_system_prompt(self) -> str:
        """Get system prompt for requirements generation"""
        return """You are a senior SAP migration specialist and business analyst with 15+ years of experience in ERP implementations and data migration projects.

Your expertise includes:
- SAP HANA SQL optimization and migration
- Cross-system data integration and ETL processes
- Business process analysis and requirements gathering
- Technical architecture design for enterprise systems

Based on the detailed SQL procedure analysis provided, generate comprehensive migration requirements documentation that will help data engineers configure the new system for data extraction.

Focus on:
1. **Data Migration Strategy** - Critical tables, dependencies, and migration sequence
2. **Join Relationship Analysis** - Complex joins that need special handling
3. **Filter Logic Consolidation** - Identifying where filters can be optimized or need special consideration
4. **Business Process Impact** - How the migration affects business operations
5. **Technical Implementation** - Specific recommendations for data engineers
6. **Risk Assessment** - Potential issues and mitigation strategies

Write in professional, technical language suitable for data engineers, solution architects, and project managers."""
    
    def _get_default_user_prompt(self, analysis_data: Dict[str, Any]) -> str:
        """Generate default user prompt from analysis data"""
        
        # Extract key metrics
        summary = analysis_data.get('summary', {})
        filter_analysis = analysis_data.get('filter_analysis', {})
        join_analysis = analysis_data.get('join_analysis', {})
        
        prompt = f"""
Based on this comprehensive SAP HANA SQL procedure analysis for migration planning, generate detailed requirements documentation:

## Migration Analysis Summary
- **Total Procedures Analyzed**: {summary.get('total_procedures', 0)}
- **Unique Tables Identified**: {summary.get('unique_tables', 0)}
- **Total Join Relationships**: {summary.get('total_joins', 0)}
- **Total Filter Conditions**: {summary.get('total_filters', 0)}

## Filter Pattern Analysis
{json.dumps(filter_analysis.get('summary', {}), indent=2)}

**Critical Filter Insights:**
{chr(10).join(filter_analysis.get('recommendations', []))}

## Join Pattern Analysis
{json.dumps(join_analysis.get('summary', {}), indent=2)}

**Join Complexity Issues:**
{chr(10).join(join_analysis.get('complexity_issues', []))}

## Data Lineage Overview
{json.dumps(analysis_data.get('data_lineage', {}).get('summary', {}), indent=2)}

Generate a comprehensive migration requirements document with:

### 1. Executive Summary
- Migration scope and complexity assessment
- Key business processes affected
- Timeline and resource implications

### 2. Data Migration Strategy
- Table migration sequence and dependencies
- Critical path analysis
- Data volume and performance considerations

### 3. Join Relationship Requirements
- Complex join patterns that need special handling
- Performance optimization recommendations
- Data integrity validation requirements

### 4. Filter Logic Optimization
- Consolidated filter requirements across procedures
- Parameter standardization opportunities
- Performance tuning recommendations

### 5. Technical Implementation Guide
- Specific configuration requirements for data engineers
- ETL process design recommendations
- Error handling and monitoring requirements

### 6. Risk Assessment and Mitigation
- Potential migration risks and issues
- Validation and testing strategies
- Rollback and recovery procedures

### 7. Success Criteria and Validation
- Key performance indicators for migration success
- Data quality validation requirements
- Business process validation checkpoints

Make the documentation actionable and specific for data engineers configuring the new system.
"""
        
        return prompt
    
    def test_connection(self, provider: str) -> LLMResponse:
        """Test LLM provider connection"""
        test_prompt = "Respond with 'Connection successful' if you can process this request."
        
        return self._call_llm_with_retry(
            provider, 
            "You are a helpful assistant.", 
            test_prompt, 
            max_retries=1
        )
