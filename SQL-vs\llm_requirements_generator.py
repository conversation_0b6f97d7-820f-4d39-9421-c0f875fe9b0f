#!/usr/bin/env python3
"""
LLM-Powered Requirements Generator for SAP HANA SQL Procedures
==============================================================

This script uses Azure OpenAI to generate comprehensive requirements documentation
from ANTLR-parsed SQL procedure metadata.

Features:
- Loads parsed SQL metadata from ANTLR analysis
- Uses Azure OpenAI GPT-4 to generate business requirements
- Creates comprehensive documentation with business context
- Generates functional and technical specifications
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
from openai import OpenAI

class LLMRequirementsGenerator:
    """Generate requirements documentation using Azure OpenAI"""
    
    def __init__(self, analysis_output_dir: str = "analysis_output"):
        self.analysis_output_dir = Path(analysis_output_dir)
        self.output_dir = self.analysis_output_dir / "llm_generated"
        self.output_dir.mkdir(exist_ok=True)
        
        # Azure OpenAI configuration
        self.endpoint = "https://models.inference.ai.azure.com"
        self.model = "gpt-4o"  # Updated to use gpt-4o instead of gpt-4.1
        self.token = "****************************************"
        
        # Initialize OpenAI client
        try:
            self.client = OpenAI(
                base_url=self.endpoint,
                api_key=self.token,
            )
        except Exception as e:
            print(f"Warning: Could not initialize OpenAI client: {e}")
            self.client = None
        
        # Load analysis data
        self.analysis_data = self._load_analysis_data()
    
    def _load_analysis_data(self) -> Dict[str, Any]:
        """Load all analysis data from JSON files"""
        data = {}
        
        files_to_load = [
            'consolidated_requirements_report.json',
            'table_catalog.json',
            'business_logic.json',
            'data_lineage.json',
            'raw_analysis_data.json'
        ]
        
        for filename in files_to_load:
            file_path = self.analysis_output_dir / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data[filename.replace('.json', '')] = json.load(f)
                    print(f"✅ Loaded {filename}")
                except Exception as e:
                    print(f"⚠️ Warning: Could not load {filename}: {e}")
            else:
                print(f"⚠️ Warning: {filename} not found")
        
        return data
    
    def _call_llm(self, system_message: str, user_message: str, temperature: float = 0.7) -> str:
        """Call Azure OpenAI API"""
        if self.client is None:
            return "Error: OpenAI client not initialized"

        try:
            response = self.client.chat.completions.create(
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                temperature=temperature,
                top_p=1.0,
                model=self.model,
                max_tokens=4000
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"❌ Error calling LLM: {e}")
            return f"Error generating content: {e}"
    
    def generate_business_requirements(self) -> str:
        """Generate business requirements documentation"""
        print("🔍 Generating business requirements...")
        
        system_message = """You are a senior business analyst specializing in SAP ERP systems. 
        You excel at analyzing SQL procedures and extracting business requirements from technical implementations.
        
        Your task is to generate comprehensive business requirements documentation based on SAP HANA SQL procedure analysis.
        Focus on:
        1. Business processes and workflows
        2. Data entities and their business purpose
        3. Business rules and logic
        4. Integration requirements
        5. Functional specifications
        
        Write in clear, business-friendly language that stakeholders can understand."""
        
        # Prepare the analysis data for the LLM
        table_info = self.analysis_data.get('table_catalog', {})
        business_logic = self.analysis_data.get('business_logic', {})
        
        user_message = f"""
        Based on the following SAP HANA SQL procedure analysis, generate comprehensive business requirements documentation:

        ## Table Analysis Summary
        {json.dumps(table_info.get('summary', {}), indent=2)}

        ## Key Tables Identified
        {json.dumps(table_info.get('tables', {}), indent=2)}

        ## Business Logic Summary  
        {json.dumps(business_logic.get('summary', {}), indent=2)}

        ## Procedures Analyzed
        {json.dumps(business_logic.get('procedures', []), indent=2)}

        Please generate:
        1. **Executive Summary** - High-level overview of the system
        2. **Business Processes** - Key business processes supported
        3. **Data Requirements** - Critical data entities and relationships
        4. **Functional Requirements** - Detailed functional specifications
        5. **Integration Requirements** - System integration needs
        6. **Business Rules** - Key business logic and constraints

        Format the output as a comprehensive requirements document.
        """
        
        return self._call_llm(system_message, user_message)
    
    def generate_technical_specifications(self) -> str:
        """Generate technical specifications"""
        print("⚙️ Generating technical specifications...")
        
        system_message = """You are a senior technical architect with expertise in SAP HANA and SQL optimization.
        
        Generate detailed technical specifications based on SQL procedure analysis, focusing on:
        1. Database schema and table relationships
        2. Performance considerations and optimization
        3. Data integration patterns
        4. Technical constraints and requirements
        5. Implementation guidelines
        
        Use technical language appropriate for developers and architects."""
        
        raw_data = self.analysis_data.get('raw_analysis_data', [])
        data_lineage = self.analysis_data.get('data_lineage', {})
        
        user_message = f"""
        Based on this SAP HANA SQL analysis, generate technical specifications:

        ## Raw Analysis Data
        {json.dumps(raw_data[:3], indent=2)}  # First 3 procedures for context

        ## Data Lineage
        {json.dumps(data_lineage, indent=2)}

        Generate:
        1. **Database Schema Overview** - Tables, relationships, and constraints
        2. **Performance Requirements** - Query optimization and indexing needs
        3. **Data Integration Architecture** - How systems connect and exchange data
        4. **Technical Constraints** - System limitations and requirements
        5. **Implementation Guidelines** - Best practices for development
        6. **Monitoring and Maintenance** - Operational considerations

        Focus on actionable technical details.
        """
        
        return self._call_llm(system_message, user_message)
    
    def generate_data_dictionary(self) -> str:
        """Generate comprehensive data dictionary"""
        print("📚 Generating data dictionary...")
        
        system_message = """You are a data governance expert specializing in SAP systems.
        
        Create a comprehensive data dictionary that explains:
        1. Business meaning of each table and column
        2. Data relationships and dependencies
        3. Data quality requirements
        4. Usage patterns and frequency
        
        Make it accessible to both technical and business users."""
        
        table_catalog = self.analysis_data.get('table_catalog', {})
        
        user_message = f"""
        Create a comprehensive data dictionary based on this table analysis:

        {json.dumps(table_catalog, indent=2)}

        For each table, provide:
        1. **Business Purpose** - What business function this table supports
        2. **Key Columns** - Most important fields and their business meaning
        3. **Relationships** - How this table connects to others
        4. **Usage Patterns** - How frequently and in what context it's used
        5. **Data Quality Notes** - Important constraints or validation rules

        Format as a professional data dictionary document.
        """
        
        return self._call_llm(system_message, user_message)
    
    def generate_migration_guide(self) -> str:
        """Generate system migration and modernization guide"""
        print("🚀 Generating migration guide...")
        
        system_message = """You are a SAP migration specialist with expertise in system modernization.
        
        Based on the SQL analysis, provide guidance for:
        1. System migration strategies
        2. Modernization opportunities
        3. Risk assessment
        4. Implementation roadmap
        
        Focus on practical, actionable recommendations."""
        
        consolidated_report = self.analysis_data.get('consolidated_requirements_report', {})
        
        user_message = f"""
        Based on this comprehensive SAP system analysis, create a migration and modernization guide:

        {json.dumps(consolidated_report, indent=2)}

        Provide:
        1. **Current State Assessment** - Analysis of existing system
        2. **Migration Strategy** - Recommended approach for system migration
        3. **Modernization Opportunities** - Areas for improvement and optimization
        4. **Risk Assessment** - Potential challenges and mitigation strategies
        5. **Implementation Roadmap** - Phased approach with timelines
        6. **Success Criteria** - How to measure migration success

        Make recommendations specific and actionable.
        """
        
        return self._call_llm(system_message, user_message)
    
    def generate_all_documentation(self):
        """Generate all types of documentation"""
        print("🤖 Starting LLM-powered requirements generation...")
        print("=" * 60)

        if not self.analysis_data:
            print("❌ No analysis data found. Please run the SQL analysis first.")
            return

        # Check if LLM is available, otherwise use fallback
        if self.client is None:
            print("⚠️ LLM not available, generating template-based documentation...")
            documents = self._generate_fallback_documentation()
        else:
            # Generate different types of documentation using LLM
            documents = {
                'business_requirements.md': self.generate_business_requirements(),
                'technical_specifications.md': self.generate_technical_specifications(),
                'data_dictionary.md': self.generate_data_dictionary(),
                'migration_guide.md': self.generate_migration_guide()
            }
        
        # Save all documents
        for filename, content in documents.items():
            if content and not content.startswith("Error"):
                file_path = self.output_dir / filename
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ Generated: {filename}")
            else:
                print(f"❌ Failed to generate: {filename}")
        
        # Generate summary report
        self._generate_summary_report(documents)
        
        print(f"\n🎉 All documentation generated in: {self.output_dir}")
    
    def _generate_summary_report(self, documents: Dict[str, str]):
        """Generate a summary report of all generated documentation"""
        summary = f"""# SAP HANA SQL Requirements Documentation Summary

Generated on: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}

## Analysis Overview
- **Total Procedures Analyzed**: {len(self.analysis_data.get('raw_analysis_data', []))}
- **Tables Identified**: {self.analysis_data.get('table_catalog', {}).get('summary', {}).get('total_tables', 0)}
- **Documentation Generated**: {len([d for d in documents.values() if d and not d.startswith('Error')])} files

## Generated Documents

### 1. Business Requirements (`business_requirements.md`)
Comprehensive business requirements documentation including processes, data needs, and functional specifications.

### 2. Technical Specifications (`technical_specifications.md`)
Detailed technical architecture, performance requirements, and implementation guidelines.

### 3. Data Dictionary (`data_dictionary.md`)
Complete data dictionary with business context for all tables and relationships.

### 4. Migration Guide (`migration_guide.md`)
Strategic guidance for system migration and modernization initiatives.

## Next Steps
1. Review generated documentation with stakeholders
2. Validate business requirements with domain experts
3. Use technical specifications for system design
4. Follow migration guide for implementation planning

## Files Location
All generated files are available in: `{self.output_dir}`
"""
        
        summary_path = self.output_dir / "README.md"
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary)

def main():
    """Main function"""
    print("LLM-Powered SAP HANA Requirements Generator")
    print("=" * 50)
    
    # Check if analysis data exists
    analysis_dir = Path("analysis_output")
    if not analysis_dir.exists():
        print("❌ Analysis output directory not found.")
        print("Please run 'python run_complete_analysis.py' first.")
        return
    
    # Initialize and run generator
    generator = LLMRequirementsGenerator()
    generator.generate_all_documentation()

if __name__ == "__main__":
    # Add pandas import for timestamp
    try:
        import pandas as pd
    except ImportError:
        # Fallback to datetime if pandas not available
        from datetime import datetime
        class pd:
            class Timestamp:
                @staticmethod
                def now():
                    return datetime.now()
    
    main()
