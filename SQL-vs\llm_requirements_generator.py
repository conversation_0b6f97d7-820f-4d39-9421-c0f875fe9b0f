#!/usr/bin/env python3
"""
LLM-Powered Requirements Generator for SAP HANA SQL Procedures
==============================================================

This script uses Azure OpenAI to generate comprehensive requirements documentation
from ANTLR-parsed SQL procedure metadata.

Features:
- Loads parsed SQL metadata from ANTLR analysis
- Uses Azure OpenAI GPT-4 to generate business requirements
- Creates comprehensive documentation with business context
- Generates functional and technical specifications
"""

import os
import json
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# Try to import OpenAI, handle different versions
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    print("Warning: OpenAI library not available")
    OPENAI_AVAILABLE = False
    OpenAI = None

class LLMRequirementsGenerator:
    """Generate requirements documentation using Azure OpenAI"""
    
    def __init__(self, analysis_output_dir: str = "analysis_output"):
        self.analysis_output_dir = Path(analysis_output_dir)
        self.output_dir = self.analysis_output_dir / "llm_generated"
        self.output_dir.mkdir(exist_ok=True)
        
        # Azure OpenAI configuration - Direct API setup
        self.endpoint = "https://models.inference.ai.azure.com"
        self.model = "gpt-4o"
        self.token = "****************************************"

        # Initialize OpenAI client with your exact configuration
        if not OPENAI_AVAILABLE:
            print("❌ OpenAI library not available")
            self.client = None
        else:
            try:
                self.client = OpenAI(
                    base_url=self.endpoint,
                    api_key=self.token,
                )
                print("✅ Azure OpenAI client initialized successfully")
            except Exception as e:
                print(f"❌ Could not initialize OpenAI client: {e}")
                print("Falling back to template-based generation...")
                self.client = None

        # Load analysis data
        self.analysis_data = self._load_analysis_data()
    
    def _load_analysis_data(self) -> Dict[str, Any]:
        """Load all analysis data from JSON files"""
        data = {}
        
        files_to_load = [
            'consolidated_requirements_report.json',
            'table_catalog.json',
            'business_logic.json',
            'data_lineage.json',
            'raw_analysis_data.json'
        ]
        
        for filename in files_to_load:
            file_path = self.analysis_output_dir / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data[filename.replace('.json', '')] = json.load(f)
                    print(f"✅ Loaded {filename}")
                except Exception as e:
                    print(f"⚠️ Warning: Could not load {filename}: {e}")
            else:
                print(f"⚠️ Warning: {filename} not found")
        
        return data
    
    def _call_llm(self, system_message: str, user_message: str, temperature: float = 0.7) -> str:
        """Call Azure OpenAI API"""
        if self.client is None:
            return "Error: OpenAI client not initialized"

        try:
            response = self.client.chat.completions.create(
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                temperature=temperature,
                top_p=1.0,
                model=self.model,
                max_tokens=4000
            )
            return response.choices[0].message.content
        except Exception as e:
            print(f"❌ Error calling LLM: {e}")
            return f"Error generating content: {e}"
    
    def generate_business_requirements(self) -> str:
        """Generate business requirements documentation"""
        print("🔍 Generating business requirements...")
        
        system_message = """You are a senior business analyst specializing in SAP ERP systems. 
        You excel at analyzing SQL procedures and extracting business requirements from technical implementations.
        
        Your task is to generate comprehensive business requirements documentation based on SAP HANA SQL procedure analysis.
        Focus on:
        1. Business processes and workflows
        2. Data entities and their business purpose
        3. Business rules and logic
        4. Integration requirements
        5. Functional specifications
        
        Write in clear, business-friendly language that stakeholders can understand."""
        
        # Prepare the analysis data for the LLM
        table_info = self.analysis_data.get('table_catalog', {})
        business_logic = self.analysis_data.get('business_logic', {})
        
        user_message = f"""
        Based on the following SAP HANA SQL procedure analysis, generate comprehensive business requirements documentation:

        ## Table Analysis Summary
        {json.dumps(table_info.get('summary', {}), indent=2)}

        ## Key Tables Identified
        {json.dumps(table_info.get('tables', {}), indent=2)}

        ## Business Logic Summary  
        {json.dumps(business_logic.get('summary', {}), indent=2)}

        ## Procedures Analyzed
        {json.dumps(business_logic.get('procedures', []), indent=2)}

        Please generate:
        1. **Executive Summary** - High-level overview of the system
        2. **Business Processes** - Key business processes supported
        3. **Data Requirements** - Critical data entities and relationships
        4. **Functional Requirements** - Detailed functional specifications
        5. **Integration Requirements** - System integration needs
        6. **Business Rules** - Key business logic and constraints

        Format the output as a comprehensive requirements document.
        """
        
        return self._call_llm(system_message, user_message)
    
    def generate_technical_specifications(self) -> str:
        """Generate technical specifications"""
        print("⚙️ Generating technical specifications...")
        
        system_message = """You are a senior technical architect with expertise in SAP HANA and SQL optimization.
        
        Generate detailed technical specifications based on SQL procedure analysis, focusing on:
        1. Database schema and table relationships
        2. Performance considerations and optimization
        3. Data integration patterns
        4. Technical constraints and requirements
        5. Implementation guidelines
        
        Use technical language appropriate for developers and architects."""
        
        raw_data = self.analysis_data.get('raw_analysis_data', [])
        data_lineage = self.analysis_data.get('data_lineage', {})
        
        user_message = f"""
        Based on this SAP HANA SQL analysis, generate technical specifications:

        ## Raw Analysis Data
        {json.dumps(raw_data[:3], indent=2)}  # First 3 procedures for context

        ## Data Lineage
        {json.dumps(data_lineage, indent=2)}

        Generate:
        1. **Database Schema Overview** - Tables, relationships, and constraints
        2. **Performance Requirements** - Query optimization and indexing needs
        3. **Data Integration Architecture** - How systems connect and exchange data
        4. **Technical Constraints** - System limitations and requirements
        5. **Implementation Guidelines** - Best practices for development
        6. **Monitoring and Maintenance** - Operational considerations

        Focus on actionable technical details.
        """
        
        return self._call_llm(system_message, user_message)
    
    def generate_data_dictionary(self) -> str:
        """Generate comprehensive data dictionary"""
        print("📚 Generating data dictionary...")
        
        system_message = """You are a data governance expert specializing in SAP systems.
        
        Create a comprehensive data dictionary that explains:
        1. Business meaning of each table and column
        2. Data relationships and dependencies
        3. Data quality requirements
        4. Usage patterns and frequency
        
        Make it accessible to both technical and business users."""
        
        table_catalog = self.analysis_data.get('table_catalog', {})
        
        user_message = f"""
        Create a comprehensive data dictionary based on this table analysis:

        {json.dumps(table_catalog, indent=2)}

        For each table, provide:
        1. **Business Purpose** - What business function this table supports
        2. **Key Columns** - Most important fields and their business meaning
        3. **Relationships** - How this table connects to others
        4. **Usage Patterns** - How frequently and in what context it's used
        5. **Data Quality Notes** - Important constraints or validation rules

        Format as a professional data dictionary document.
        """
        
        return self._call_llm(system_message, user_message)
    
    def generate_migration_guide(self) -> str:
        """Generate system migration and modernization guide"""
        print("🚀 Generating migration guide...")
        
        system_message = """You are a SAP migration specialist with expertise in system modernization.
        
        Based on the SQL analysis, provide guidance for:
        1. System migration strategies
        2. Modernization opportunities
        3. Risk assessment
        4. Implementation roadmap
        
        Focus on practical, actionable recommendations."""
        
        consolidated_report = self.analysis_data.get('consolidated_requirements_report', {})
        
        user_message = f"""
        Based on this comprehensive SAP system analysis, create a migration and modernization guide:

        {json.dumps(consolidated_report, indent=2)}

        Provide:
        1. **Current State Assessment** - Analysis of existing system
        2. **Migration Strategy** - Recommended approach for system migration
        3. **Modernization Opportunities** - Areas for improvement and optimization
        4. **Risk Assessment** - Potential challenges and mitigation strategies
        5. **Implementation Roadmap** - Phased approach with timelines
        6. **Success Criteria** - How to measure migration success

        Make recommendations specific and actionable.
        """
        
        return self._call_llm(system_message, user_message)

    def test_llm_connection(self) -> bool:
        """Test if LLM connection is working"""
        print("🔍 Testing LLM connection...")

        if self.client is None:
            print("❌ LLM client not initialized")
            return False

        try:
            test_response = self._call_llm(
                "You are a helpful assistant.",
                "Say 'Hello, SAP HANA analysis is ready!' if you can respond."
            )

            if test_response and not test_response.startswith("Error"):
                print("✅ LLM connection successful!")
                print(f"Response: {test_response}")
                return True
            else:
                print(f"❌ LLM test failed: {test_response}")
                return False

        except Exception as e:
            print(f"❌ LLM connection test failed: {e}")
            return False

    def _generate_fallback_documentation(self) -> Dict[str, str]:
        """Generate template-based documentation when LLM is not available"""
        print("📝 Generating template-based documentation...")

        table_catalog = self.analysis_data.get('table_catalog', {})
        tables = table_catalog.get('tables', {})
        summary = table_catalog.get('summary', {})

        business_requirements = f"""# Business Requirements Documentation

## Executive Summary
This document outlines the business requirements for the SAP HANA system based on analysis of {summary.get('total_tables', 0)} tables across multiple SQL procedures.

## Key Business Entities

### Master Data Tables
{self._format_tables_by_purpose(tables, 'Master')}

### Transaction Tables
{self._format_tables_by_purpose(tables, 'Order')}

### Document Tables
{self._format_tables_by_purpose(tables, 'Document')}

## Business Processes Identified
1. **Customer Order Management** - Processing and tracking customer orders
2. **Material Management** - Inventory and material master data maintenance
3. **Vendor Payment Processing** - Managing vendor payments and aging analysis

## Functional Requirements
- Customer order retrieval and reporting
- Material stock calculation and monitoring
- Vendor payment analysis and aging reports
- Integration between sales, inventory, and financial modules

## Data Requirements
- Real-time access to customer, material, and vendor master data
- Historical transaction data for reporting and analysis
- Integration with SAP ECC/S4HANA source systems
"""

        technical_specs = f"""# Technical Specifications

## Database Schema Overview
Total Tables Analyzed: {summary.get('total_tables', 0)}

### Core Tables
{self._format_table_list(tables)}

## Performance Considerations
- Implement proper indexing on frequently joined columns
- Consider partitioning for large transaction tables
- Optimize queries with date range filters

## Integration Architecture
- Source System: SAP ECC/S4HANA
- Target System: SAP HANA
- Data Transfer: Real-time or batch processing
- API Integration: REST/OData services

## Technical Constraints
- HANA SQL compliance required
- Unicode support for international characters
- Multi-client architecture support
"""

        data_dictionary = f"""# Data Dictionary

## Table Overview
{self._generate_table_dictionary(tables)}

## Common Patterns
- Client field (MANDT) for multi-tenancy
- Document number patterns (10-digit alphanumeric)
- Date fields in YYYYMMDD format
- Currency and quantity fields with decimal precision
"""

        migration_guide = f"""# Migration and Modernization Guide

## Current State Assessment
- {len(self.analysis_data.get('raw_analysis_data', []))} SQL procedures analyzed
- {summary.get('total_tables', 0)} tables identified
- Mixed transaction and master data patterns

## Migration Strategy
1. **Phase 1**: Master data migration (Customer, Material, Vendor)
2. **Phase 2**: Transaction data migration (Orders, Documents)
3. **Phase 3**: Procedure and logic migration
4. **Phase 4**: Integration and testing

## Modernization Opportunities
- Convert procedures to HANA-native SQLScript
- Implement column store optimization
- Add real-time analytics capabilities
- Enhance with machine learning features

## Risk Mitigation
- Comprehensive data validation
- Parallel run testing
- Rollback procedures
- Performance monitoring
"""

        return {
            'business_requirements.md': business_requirements,
            'technical_specifications.md': technical_specs,
            'data_dictionary.md': data_dictionary,
            'migration_guide.md': migration_guide
        }

    def _format_tables_by_purpose(self, tables: Dict, purpose_keyword: str) -> str:
        """Format tables that match a purpose keyword"""
        matching_tables = []
        for table_name, table_info in tables.items():
            if purpose_keyword.lower() in table_info.get('estimated_purpose', '').lower():
                matching_tables.append(f"- **{table_name}**: {table_info.get('estimated_purpose', 'Business Data Table')}")

        return '\n'.join(matching_tables) if matching_tables else f"- No {purpose_keyword} tables identified"

    def _format_table_list(self, tables: Dict) -> str:
        """Format a simple list of tables"""
        table_list = []
        for table_name, table_info in tables.items():
            usage = table_info.get('usage_frequency', 0)
            purpose = table_info.get('estimated_purpose', 'Business Data Table')
            table_list.append(f"- **{table_name}** (Used {usage} times) - {purpose}")

        return '\n'.join(table_list)

    def _generate_table_dictionary(self, tables: Dict) -> str:
        """Generate detailed table dictionary"""
        dictionary = []
        for table_name, table_info in tables.items():
            entry = f"""
### {table_name}
- **Purpose**: {table_info.get('estimated_purpose', 'Business Data Table')}
- **Usage Frequency**: {table_info.get('usage_frequency', 0)} times
- **Used In Procedures**: {', '.join(table_info.get('used_in_procedures', []))}
- **Column Count**: {table_info.get('column_count', 0)}
"""
            dictionary.append(entry)

        return '\n'.join(dictionary)

    def generate_all_documentation(self):
        """Generate all types of documentation"""
        print("🤖 Starting LLM-powered requirements generation...")
        print("=" * 60)

        if not self.analysis_data:
            print("❌ No analysis data found. Please run the SQL analysis first.")
            return

        # Check if LLM is available, otherwise use fallback
        if self.client is None:
            print("⚠️ LLM not available, generating template-based documentation...")
            documents = self._generate_fallback_documentation()
        else:
            # Generate different types of documentation using LLM
            documents = {
                'business_requirements.md': self.generate_business_requirements(),
                'technical_specifications.md': self.generate_technical_specifications(),
                'data_dictionary.md': self.generate_data_dictionary(),
                'migration_guide.md': self.generate_migration_guide()
            }
        
        # Save all documents
        for filename, content in documents.items():
            if content and not content.startswith("Error"):
                file_path = self.output_dir / filename
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ Generated: {filename}")
            else:
                print(f"❌ Failed to generate: {filename}")
        
        # Generate summary report
        self._generate_summary_report(documents)
        
        print(f"\n🎉 All documentation generated in: {self.output_dir}")
    
    def _generate_summary_report(self, documents: Dict[str, str]):
        """Generate a summary report of all generated documentation"""
        summary = f"""# SAP HANA SQL Requirements Documentation Summary

Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Analysis Overview
- **Total Procedures Analyzed**: {len(self.analysis_data.get('raw_analysis_data', []))}
- **Tables Identified**: {self.analysis_data.get('table_catalog', {}).get('summary', {}).get('total_tables', 0)}
- **Documentation Generated**: {len([d for d in documents.values() if d and not d.startswith('Error')])} files

## Generated Documents

### 1. Business Requirements (`business_requirements.md`)
Comprehensive business requirements documentation including processes, data needs, and functional specifications.

### 2. Technical Specifications (`technical_specifications.md`)
Detailed technical architecture, performance requirements, and implementation guidelines.

### 3. Data Dictionary (`data_dictionary.md`)
Complete data dictionary with business context for all tables and relationships.

### 4. Migration Guide (`migration_guide.md`)
Strategic guidance for system migration and modernization initiatives.

## Next Steps
1. Review generated documentation with stakeholders
2. Validate business requirements with domain experts
3. Use technical specifications for system design
4. Follow migration guide for implementation planning

## Files Location
All generated files are available in: `{self.output_dir}`
"""
        
        summary_path = self.output_dir / "README.md"
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary)

def main():
    """Main function"""
    print("LLM-Powered SAP HANA Requirements Generator")
    print("=" * 50)
    
    # Check if analysis data exists
    analysis_dir = Path("analysis_output")
    if not analysis_dir.exists():
        print("❌ Analysis output directory not found.")
        print("Please run 'python run_complete_analysis.py' first.")
        return
    
    # Initialize and run generator
    generator = LLMRequirementsGenerator()

    # Test LLM connection first
    if generator.client is not None:
        generator.test_llm_connection()

    generator.generate_all_documentation()

if __name__ == "__main__":
    main()
