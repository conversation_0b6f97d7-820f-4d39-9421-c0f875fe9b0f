
CREATE PROCEDURE calculate_material_stock(
    IN p_plant NVARCHAR(4),
    IN p_material_group NVARCHAR(9)
)
LANGUAGE SQLSCRIPT
SQL SECURITY INVOKER
READS SQL DATA AS
BEGIN
    DECLARE v_total_stock DECIMAL(15,3);
    
    SELECT 
        s.matnr as material_number,
        m.maktx as material_description,
        s.werks as plant,
        s.lgort as storage_location,
        SUM(s.labst) as available_stock,
        SUM(s.insme) as quality_inspection_stock,
        SUM(s.speme) as blocked_stock,
        m.meins as base_unit
    FROM mard s
    INNER JOIN mara m ON s.matnr = m.matnr
    INNER JOIN makt mt ON m.matnr = mt.matnr
    WHERE s.werks = p_plant
      AND m.matkl = p_material_group
      AND s.labst > 0
    GROUP BY s.matnr, m.maktx, s.werks, s.lgort, m.meins
    ORDER BY s.matnr, s.lgort;
END;
