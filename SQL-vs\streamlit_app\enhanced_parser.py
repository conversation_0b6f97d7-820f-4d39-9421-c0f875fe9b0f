#!/usr/bin/env python3
"""
Enhanced SQL Parser for LLM Input
=================================

A focused parser that ensures the LLM gets proper abstraction data
with correct filter values and table information.
"""

import re
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass
from collections import defaultdict

@dataclass
class EnhancedFilterCondition:
    """Enhanced filter condition with proper value extraction"""
    table: str
    column: str
    operator: str
    values: List[str]
    condition_text: str
    procedure_id: str

@dataclass
class EnhancedTableInfo:
    """Enhanced table information"""
    name: str
    columns: Set[str]
    procedures: List[str]
    filters: List[EnhancedFilterCondition]
    joins: List[str]

class EnhancedSQLParser:
    """Enhanced SQL parser focused on LLM data quality"""
    
    def __init__(self):
        self.sap_tables = {
            'VBAK': 'Sales Document Header',
            'VBAP': 'Sales Document Items',
            'VBRK': 'Billing Document Header', 
            'VBRP': 'Billing Document Items',
            'BKPF': 'Accounting Document Header',
            'BSEG': 'Accounting Document Line Items',
            'CDHDR': 'Change Document Header',
            'CDPOS': 'Change Document Items',
            'KNA1': 'Customer Master',
            'LFA1': 'Vendor Master',
            'MARA': 'Material Master'
        }
    
    def extract_comprehensive_data(self, sql_text: str, procedure_id: str) -> Dict[str, Any]:
        """Extract comprehensive data for LLM input"""
        
        # Clean SQL
        cleaned_sql = self._clean_sql_for_analysis(sql_text)
        
        # Extract components
        tables = self._extract_tables_enhanced(cleaned_sql)
        filters = self._extract_filters_enhanced(cleaned_sql, procedure_id)
        joins = self._extract_joins_enhanced(cleaned_sql)
        
        # Create enhanced table info
        table_info = {}
        for table_name in tables:
            table_filters = [f for f in filters if f.table == table_name]
            table_joins = [j for j in joins if table_name in j]
            
            table_info[table_name] = EnhancedTableInfo(
                name=table_name,
                columns=self._extract_columns_for_table(cleaned_sql, table_name),
                procedures=[procedure_id],
                filters=table_filters,
                joins=table_joins
            )
        
        return {
            'procedure_id': procedure_id,
            'tables': table_info,
            'filters': filters,
            'joins': joins,
            'raw_sql': sql_text
        }
    
    def _clean_sql_for_analysis(self, sql_text: str) -> str:
        """Clean SQL while preserving filter values"""
        # Remove comments
        sql_text = re.sub(r'/\*.*?\*/', '', sql_text, flags=re.DOTALL)
        sql_text = re.sub(r'--.*?\n', '\n', sql_text)
        
        # Keep string literals - they contain actual filter values
        # Normalize whitespace
        sql_text = re.sub(r'\s+', ' ', sql_text)
        
        return sql_text
    
    def _extract_tables_enhanced(self, sql_text: str) -> Set[str]:
        """Extract table names with enhanced accuracy"""
        tables = set()
        
        # Pattern for FROM and JOIN clauses
        table_patterns = [
            r'FROM\s+([A-Z][A-Z0-9_]+)(?:\s+AS\s+\w+)?',
            r'JOIN\s+([A-Z][A-Z0-9_]+)(?:\s+AS\s+\w+)?',
            r'INNER\s+JOIN\s+([A-Z][A-Z0-9_]+)(?:\s+AS\s+\w+)?',
            r'LEFT\s+JOIN\s+([A-Z][A-Z0-9_]+)(?:\s+AS\s+\w+)?',
            r'UPDATE\s+([A-Z][A-Z0-9_]+)',
            r'INSERT\s+INTO\s+([A-Z][A-Z0-9_]+)'
        ]
        
        for pattern in table_patterns:
            matches = re.finditer(pattern, sql_text, re.IGNORECASE)
            for match in matches:
                table_name = match.group(1).upper()
                # Filter known SAP tables or reasonable table names
                if (len(table_name) >= 3 and 
                    table_name.isalnum() and 
                    not table_name.isdigit()):
                    tables.add(table_name)
        
        return tables
    
    def _extract_filters_enhanced(self, sql_text: str, procedure_id: str) -> List[EnhancedFilterCondition]:
        """Extract filters with proper value preservation"""
        filters = []

        # Multiple patterns to catch different filter formats
        patterns = [
            # Pattern for IN clauses with parentheses
            r'(\w+)\.(\w+)\s+(IN)\s*\([^)]+\)',
            # Pattern for other operators
            r'(\w+)\.(\w+)\s*(=|!=|<>|<|>|<=|>=|LIKE|BETWEEN)\s*([^A-Z\n]+?)(?=\s+AND\s+|\s+OR\s+|;|\)|$)',
        ]

        for pattern in patterns:
            matches = re.finditer(pattern, sql_text, re.IGNORECASE)

            for match in matches:
                table = match.group(1).upper()
                column = match.group(2).upper()
                operator = match.group(3).upper()

                if operator == 'IN':
                    # For IN clauses, get the full match including parentheses
                    value_part = match.group(0).split(operator, 1)[1].strip()
                else:
                    value_part = match.group(4).strip()

                # Extract actual values
                values = self._extract_values_properly(value_part, operator)

                filter_cond = EnhancedFilterCondition(
                    table=table,
                    column=column,
                    operator=operator,
                    values=values,
                    condition_text=f"{table}.{column} {operator} {value_part}",
                    procedure_id=procedure_id
                )
                filters.append(filter_cond)

        return filters
    
    def _extract_values_properly(self, value_part: str, operator: str) -> List[str]:
        """Extract filter values properly - handles 'OR', 'TA', 'ZOR' correctly"""
        values = []
        
        if operator == 'IN':
            # Find all quoted strings in the value part
            quoted_pattern = r"'([^']*)'"
            quoted_matches = re.findall(quoted_pattern, value_part)
            values.extend(quoted_matches)
            
            # If no quoted strings found, try parentheses content
            if not values:
                paren_pattern = r'\((.*?)\)'
                paren_match = re.search(paren_pattern, value_part)
                if paren_match:
                    content = paren_match.group(1)
                    # Split by comma and clean
                    raw_values = [v.strip().strip("'\"") for v in content.split(',')]
                    values.extend([v for v in raw_values if v])
        
        elif operator == 'BETWEEN':
            # Extract BETWEEN values
            between_pattern = r'(.+?)\s+AND\s+(.+)'
            match = re.search(between_pattern, value_part, re.IGNORECASE)
            if match:
                val1 = match.group(1).strip().strip("'\"")
                val2 = match.group(2).strip().strip("'\"")
                values = [val1, val2]
        
        else:
            # Single value
            clean_value = value_part.strip().strip("'\"")
            if clean_value:
                values = [clean_value]
        
        return values
    
    def _extract_columns_for_table(self, sql_text: str, table_name: str) -> Set[str]:
        """Extract columns used for a specific table"""
        columns = set()
        
        # Pattern for table.column references
        column_pattern = rf'{table_name}\.(\w+)'
        matches = re.finditer(column_pattern, sql_text, re.IGNORECASE)
        
        for match in matches:
            column = match.group(1).upper()
            columns.add(column)
        
        return columns
    
    def _extract_joins_enhanced(self, sql_text: str) -> List[str]:
        """Extract join conditions"""
        joins = []
        
        join_pattern = r'(INNER\s+JOIN|LEFT\s+JOIN|RIGHT\s+JOIN|FULL\s+JOIN|JOIN)\s+(\w+)(?:\s+AS\s+\w+)?\s+ON\s+([^A-Z\n]+?)(?=\s+(?:INNER|LEFT|RIGHT|FULL|JOIN|WHERE|GROUP|ORDER|$))'
        
        matches = re.finditer(join_pattern, sql_text, re.IGNORECASE)
        
        for match in matches:
            join_type = match.group(1).strip()
            table = match.group(2).upper()
            condition = match.group(3).strip()
            joins.append(f"{join_type} {table} ON {condition}")
        
        return joins

def test_enhanced_parser():
    """Test the enhanced parser"""
    print("🔍 Testing Enhanced Parser...")
    
    parser = EnhancedSQLParser()
    
    test_sql = '''
    PROCEDURE "PR_ORDER_DELIVERY_ANALYSIS" AS
    BEGIN
    SELECT VBAK.VBELN, VBAK.AUART, VBAP.POSNR
    FROM VBAK 
    INNER JOIN VBAP ON VBAK.VBELN = VBAP.VBELN
    WHERE VBAK.AUART IN ('OR', 'TA', 'ZOR')
    AND VBAP.ERDAT > '20240101';
    END;
    '''
    
    result = parser.extract_comprehensive_data(test_sql, "PR_ORDER_DELIVERY_ANALYSIS")
    
    print(f"📊 Results:")
    print(f"   Tables: {list(result['tables'].keys())}")
    print(f"   Filters: {len(result['filters'])}")
    
    for filter_cond in result['filters']:
        print(f"   Filter: {filter_cond.table}.{filter_cond.column} {filter_cond.operator} {filter_cond.values}")
    
    return result

if __name__ == "__main__":
    test_enhanced_parser()
